package com.teyuntong.goods.service.schedule;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/10/17 20:16
 */
@Slf4j
@Component
public class UserSchedule {

    @XxlJob("demoJobHandler")
    public void demoJobHandler() {
        log.info("Hello World!");
    }
}
