package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.permission.service.ExcellentGoodsCardRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/26 20:37
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "excellentGoodsCardRemoteService",
        fallbackFactory = ExcellentGoodsCardRemoteService.ExcellentGoodsCardRemoteServiceFallbackFactory.class)
public interface ExcellentGoodsCardRemoteService extends ExcellentGoodsCardRpcService {
    @Component
    class ExcellentGoodsCardRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ExcellentGoodsCardRemoteService> {
        protected ExcellentGoodsCardRemoteServiceFallbackFactory() {
            super(true, ExcellentGoodsCardRemoteService.class);
        }
    }
}
