package com.teyuntong.goods.service.service.remote.goods.search;

import com.teyuntong.goods.search.client.transport.service.SimilarityGoodsRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/05/19 11:27
 */
@FeignClient(name = "tyt-goods-search", path = "goods-search", contextId = "SimilarityGoodsRpcService",
        fallbackFactory = SimilarityGoodsRemoteService.SimilarityGoodsRemoteServiceFallback.class)
public interface SimilarityGoodsRemoteService extends SimilarityGoodsRpcService {
    @Component
    class SimilarityGoodsRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<SimilarityGoodsRemoteService> {
        public SimilarityGoodsRemoteServiceFallback() {
            super(true, SimilarityGoodsRemoteService.class);
        }
    }
}
