package com.teyuntong.goods.service.service.remote.basic;

/**
 * ABTestRpcService
 *
 * <AUTHOR>
 * @since 2024-11-05 17:01
 */

import com.teyuntong.infra.basic.resource.client.tytabtest.service.ABTestRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "ABTestRpcService", fallbackFactory = ABTestRemoteService.ABTestRemoteServiceFallbackFactory.class)
public interface ABTestRemoteService extends ABTestRpcService {
    @Component
    class ABTestRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ABTestRemoteService> {
        protected ABTestRemoteServiceFallbackFactory() {
            super(true, ABTestRemoteService.class);
        }
    }
}
