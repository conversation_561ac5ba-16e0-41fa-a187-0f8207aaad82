package com.teyuntong.goods.service.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.TransportOrderSnapshotRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * trade 订单接口
 *
 * <AUTHOR>
 * @since 2024/08/13 10:50
 */
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "TransportOrderSnapshotRpcService",
        fallbackFactory = OrderSnapshotRemoteService.OrderSnapshotRemoteServiceFallback.class)
public interface OrderSnapshotRemoteService extends TransportOrderSnapshotRpcService {
    @Component
    class OrderSnapshotRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<OrderSnapshotRemoteService> {
        public OrderSnapshotRemoteServiceFallback() {
            super(true, OrderSnapshotRemoteService.class);
        }
    }
}
