package com.teyuntong.goods.service.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.tytcity.service.MapDictRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * city测试远程调用
 *
 * <AUTHOR>
 * @since 2024/09/19 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "mapDictRemoteService",
        fallbackFactory = MapDictRemoteService.MapDictRemoteServiceFallback.class)
public interface MapDictRemoteService extends MapDictRpcService {

    @Component
    class MapDictRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<MapDictRemoteService> {
        public MapDictRemoteServiceFallback() {
            super(true, MapDictRemoteService.class);
        }
    }

}
