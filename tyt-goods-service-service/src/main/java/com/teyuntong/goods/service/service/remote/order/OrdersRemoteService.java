package com.teyuntong.goods.service.service.remote.order;


import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.OrdersRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * OrdersRemoteService
 *
 * <AUTHOR>
 * @since 2024-12-10 13:01
 */
@Service
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "ordersRemoteService",
        fallbackFactory = OrdersRemoteService.OrdersRemoteServiceFallbackFactory.class)
public interface OrdersRemoteService extends OrdersRpcService {
    @Component
    class OrdersRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<OrdersRemoteService> {
        protected OrdersRemoteServiceFallbackFactory() {
            super(true, OrdersRemoteService.class);
        }
    }
}
