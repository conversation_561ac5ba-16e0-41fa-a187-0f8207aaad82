package com.teyuntong.goods.service.service.remote.market;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.market.activity.client.coupon.service.CouponRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/03/20 13:31
 */
@Service
@FeignClient(name = "tyt-market-activity", path = "market-activity", contextId = "couponRemoteService", fallbackFactory = CouponRemoteService.CouponRemoteServiceFallbackFactory.class)
public interface CouponRemoteService extends CouponRpcService {

    @Component
    class CouponRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CouponRemoteService> {
        protected CouponRemoteServiceFallbackFactory() {
            super(true, CouponRemoteService.class);
        }
    }

}
