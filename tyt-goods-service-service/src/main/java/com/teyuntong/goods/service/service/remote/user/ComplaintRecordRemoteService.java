package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.goods.service.StrategyGrantRpcService;
import com.teyuntong.user.service.client.user.service.ComplaintRecordRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * ComplaintRecordRpcService
 *
 * <AUTHOR>
 * @since 2024-11-06 14:36
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "ComplaintRecordRpcService",
        fallbackFactory = ComplaintRecordRemoteService.ComplaintRecordRemoteFallbackFactory.class)
public interface ComplaintRecordRemoteService extends ComplaintRecordRpcService {
    @Component
    @Slf4j
    class ComplaintRecordRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ComplaintRecordRemoteService> {
        ComplaintRecordRemoteFallbackFactory() {
            super(true, ComplaintRecordRemoteService.class);
        }
    }
}
