package com.teyuntong.goods.service.service.remote.outer;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.cticloud.axb.service.AxbRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "AxbRpcService", fallbackFactory = AXBCommonRemoteService.AXBCommonRemoteServiceFallbackFactory.class)
public interface AXBCommonRemoteService extends AxbRpcService {


    @Component
    class AXBCommonRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<AXBCommonRemoteService> {
        protected AXBCommonRemoteServiceFallbackFactory() {
            super(true, AXBCommonRemoteService.class);
        }
    }
}
