package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.enterprise.service.InvoiceEnterpriseRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "InvoiceEnterpriseRemoteService",
        fallbackFactory = InvoiceEnterpriseRemoteService.InvoiceEnterpriseRemoteServiceFallbackFactory.class)
public interface InvoiceEnterpriseRemoteService extends InvoiceEnterpriseRpcService {
    @Component
    class InvoiceEnterpriseRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InvoiceEnterpriseRemoteService> {
        protected InvoiceEnterpriseRemoteServiceFallbackFactory() {
            super(true, InvoiceEnterpriseRemoteService.class);
        }
    }
}
