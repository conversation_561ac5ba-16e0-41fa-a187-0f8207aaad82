package com.teyuntong.goods.service.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.infofee.service.InfoFeeRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 履约服务client
 *
 * <AUTHOR>
 * @since 2024-1-16 15:32:43
 */
@FeignClient(name = "tyt-trade-service", contextId = "ordersRpcService", path = "trade", fallbackFactory = InfoFeeRemoteService.InfoFeeRemoteFallbackFactory.class)
public interface InfoFeeRemoteService extends InfoFeeRpcService {
    @Component
    class InfoFeeRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InfoFeeRemoteService> {
        protected InfoFeeRemoteFallbackFactory() {
            super(true, InfoFeeRemoteService.class);
        }
    }
}