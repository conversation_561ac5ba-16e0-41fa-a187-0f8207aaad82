package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.limit.service.UserLimitRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userLimitRemoteService", fallbackFactory = UserLimitRemoteService.UserLimitRemoteServiceFallback.class)
public interface UserLimitRemoteService extends UserLimitRpcService {

    @Component
    class UserLimitRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<UserLimitRemoteService> {
        public UserLimitRemoteServiceFallback() {
            super(true, UserLimitRemoteService.class);
        }
    }
}
