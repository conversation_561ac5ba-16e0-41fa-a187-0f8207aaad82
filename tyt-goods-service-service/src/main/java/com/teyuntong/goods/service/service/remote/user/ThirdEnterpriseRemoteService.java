package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.enterprise.service.ThirdEnterpriseRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/26 20:37
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "thirdEnterpriseRemoteService",
        fallbackFactory = ThirdEnterpriseRemoteService.ThirdEnterpriseRemoteServiceFallbackFactory.class)
public interface ThirdEnterpriseRemoteService extends ThirdEnterpriseRpcService {
    @Component
    class ThirdEnterpriseRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ThirdEnterpriseRemoteService> {
        protected ThirdEnterpriseRemoteServiceFallbackFactory() {
            super(true, ThirdEnterpriseRemoteService.class);
        }
    }
}
