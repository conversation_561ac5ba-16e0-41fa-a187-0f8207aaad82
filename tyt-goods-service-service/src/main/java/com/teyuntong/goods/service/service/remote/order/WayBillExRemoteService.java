package com.teyuntong.goods.service.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.waybillex.service.WayBillExRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/2/8 10:31
 */
@Service
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "wayBillExRemoteService",
        fallbackFactory = WayBillExRemoteService.WayBillExRemoteServiceFallbackFactory.class)
public interface WayBillExRemoteService extends WayBillExRpcService {
    @Component
    class WayBillExRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<WayBillExRemoteService> {
        protected WayBillExRemoteServiceFallbackFactory() {
            super(true, WayBillExRemoteService.class);
        }
    }
}
