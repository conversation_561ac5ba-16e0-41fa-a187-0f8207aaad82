package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.goods.service.StrategyGrantRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * StrategyGrantRpcService
 *
 * <AUTHOR>
 * @since 2024-11-06 14:36
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "StrategyGrantRpcService",
        fallbackFactory = StrategyGrantRemoteService.StrategyGrantRemoteFallbackFactory.class)
public interface StrategyGrantRemoteService extends StrategyGrantRpcService {
    @Component
    @Slf4j
    class StrategyGrantRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<StrategyGrantRemoteService> {
        StrategyGrantRemoteFallbackFactory() {
            super(true, StrategyGrantRemoteService.class);
        }
    }
}
