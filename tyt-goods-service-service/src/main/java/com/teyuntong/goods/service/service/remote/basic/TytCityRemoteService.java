package com.teyuntong.goods.service.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.tytcity.service.TytCityRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * city测试远程调用
 *
 * <AUTHOR>
 * @since 2024/09/19 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "tytCityRemoteService",
        fallbackFactory = TytCityRemoteService.ConfigRemoteServiceFallback.class)
public interface TytCityRemoteService extends TytCityRpcService {

    @Component
    class ConfigRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<TytCityRemoteService> {
        public ConfigRemoteServiceFallback() {
            super(true, TytCityRemoteService.class);
        }
    }

}
