package com.teyuntong.goods.service.service.remote.trace;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.trace.client.location.CarLocationRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-trace", path = "trace", contextId = "CarLocationRemoteService",
        fallbackFactory = CarLocationRemoteService.CarLocationRemoteFallbackFactory.class)
public interface CarLocationRemoteService extends CarLocationRpcService {

    @Component
    class CarLocationRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CarLocationRemoteService>{
        protected CarLocationRemoteFallbackFactory() {
            super(true, CarLocationRemoteService.class);
        }
    }
}