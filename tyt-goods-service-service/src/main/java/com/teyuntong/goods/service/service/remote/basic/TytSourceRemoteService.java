package com.teyuntong.goods.service.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.source.service.TytSourceRpcService;
import com.teyuntong.infra.basic.resource.client.tytcity.service.TytCityRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * source测试远程调用
 *
 * <AUTHOR>
 * @since 2024/09/19 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "tytSourceRemoteService",
        fallbackFactory = TytSourceRemoteService.TytSourceRemoteServiceFallback.class)
public interface TytSourceRemoteService extends TytSourceRpcService {

    @Component
    class TytSourceRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<TytSourceRemoteService> {
        public TytSourceRemoteServiceFallback() {
            super(true, TytSourceRemoteService.class);
        }
    }

}
