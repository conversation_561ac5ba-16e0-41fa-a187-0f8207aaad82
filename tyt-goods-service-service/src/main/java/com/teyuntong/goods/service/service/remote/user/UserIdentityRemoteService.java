package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.UserIdentityRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userIdentityRemoteService",
        fallbackFactory = UserIdentityRemoteService.UserIdentityRemoteFallbackFactory.class)
public interface UserIdentityRemoteService extends UserIdentityRpcService {

    @Component
    class UserIdentityRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<UserIdentityRemoteService> {
        protected UserIdentityRemoteFallbackFactory() {
            super(true, UserIdentityRemoteService.class);
        }
    }
}