package com.teyuntong.goods.service.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.pubresource.service.PublicResourceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "PublicResourceRpcService", fallbackFactory = PublicResourceRemoteService.PublicResourceRemoteServiceFallbackFactory.class)
public interface PublicResourceRemoteService extends PublicResourceRpcService {


    @Component
    class PublicResourceRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<PublicResourceRemoteService> {
        protected PublicResourceRemoteServiceFallbackFactory() {
            super(true, PublicResourceRemoteService.class);
        }
    }
}
