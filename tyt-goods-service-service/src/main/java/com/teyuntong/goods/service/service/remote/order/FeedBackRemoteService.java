package com.teyuntong.goods.service.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.feedBack.service.FeedBackRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 履约服务client
 *
 * <AUTHOR>
 * @since 2025-02-17 15:32:43
 */
@FeignClient(name = "tyt-trade-service", contextId = "feedBackRemoteService", path = "trade",
        fallbackFactory = FeedBackRemoteService.FeedBackRemoteFallbackFactory.class)
public interface FeedBackRemoteService extends FeedBackRpcService {
    @Component
    class FeedBackRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<FeedBackRemoteService> {
        protected FeedBackRemoteFallbackFactory() {
            super(true, FeedBackRemoteService.class);
        }
    }
}