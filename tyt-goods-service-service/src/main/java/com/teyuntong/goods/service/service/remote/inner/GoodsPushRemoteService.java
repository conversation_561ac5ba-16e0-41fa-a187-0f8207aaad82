package com.teyuntong.goods.service.service.remote.inner;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.push.service.GoodsPushRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 发送推送
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "GoodsPushRpcService",
        fallbackFactory = GoodsPushRemoteService.GoodsPushRemoteServiceFallback.class)
public interface GoodsPushRemoteService extends GoodsPushRpcService {

    @Component
    class GoodsPushRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<GoodsPushRemoteService> {
        public GoodsPushRemoteServiceFallback() {
            super(true, GoodsPushRemoteService.class);
        }
    }
}
