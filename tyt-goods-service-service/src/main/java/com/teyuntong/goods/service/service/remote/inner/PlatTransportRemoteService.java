package com.teyuntong.goods.service.service.remote.inner;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.plat.transport.service.TransportRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "platTransportRemoteService",
        fallbackFactory = PlatTransportRemoteService.PlatTransportRemoteServiceFallback.class)
public interface PlatTransportRemoteService extends TransportRpcService {

    @Component
    class PlatTransportRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<PlatTransportRemoteService> {
        public PlatTransportRemoteServiceFallback() {
            super(true, PlatTransportRemoteService.class);
        }
    }
}
