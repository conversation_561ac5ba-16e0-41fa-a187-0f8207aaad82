package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.permission.service.UserPermissionRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * UserPermissionRpcService
 *
 * <AUTHOR>
 * @since 2024-11-06 14:36
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "UserPermissionRpcService", fallbackFactory = UserPermissionRemoteService.UserPermissionRemoteFallbackFactory.class)
public interface UserPermissionRemoteService extends UserPermissionRpcService {
    @Component
    @Slf4j
    class UserPermissionRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<UserPermissionRemoteService> {
        UserPermissionRemoteFallbackFactory() {
            super(true, UserPermissionRemoteService.class);
        }
    }
}
