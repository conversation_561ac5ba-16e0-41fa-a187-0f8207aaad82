package com.teyuntong.goods.service.service.remote.goods.search;

import com.teyuntong.goods.search.client.transport.service.TransportLabelRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/05/19 11:27
 */
@FeignClient(name = "tyt-goods-search", path = "goods-search", contextId = "TransportLabelRpcService",
        fallbackFactory = TransportSearchRemoteService.TransportSearchRemoteServiceFallback.class)
public interface TransportSearchRemoteService extends TransportLabelRpcService {
    @Component
    class TransportSearchRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<TransportSearchRemoteService> {
        public TransportSearchRemoteServiceFallback() {
            super(true, TransportSearchRemoteService.class);
        }
    }
}
