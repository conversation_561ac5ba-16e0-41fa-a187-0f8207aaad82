package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.custom.service.CsMaintainedCustomRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/03/06 17:05
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "csMaintainedCustomRemoteService",
        fallbackFactory = CsMaintainedCustomRemoteService.CsMaintainedCustomRemoteServiceFallbackFactory.class)
public interface CsMaintainedCustomRemoteService extends CsMaintainedCustomRpcService {
    @Component
    class CsMaintainedCustomRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CsMaintainedCustomRemoteService> {
        protected CsMaintainedCustomRemoteServiceFallbackFactory() {
            super(true, CsMaintainedCustomRemoteService.class);
        }
    }
}
