package com.teyuntong.goods.service.service.remote.outer;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.cticloud.axb.service.AxbRpcService;
import com.teyuntong.outer.export.service.client.cticloud.task.service.TaskRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "TaskRpcService",
        fallbackFactory = AutoCallTaskRemoteService.AutoCallTaskRemoteServiceFallbackFactory.class)
public interface AutoCallTaskRemoteService extends TaskRpcService {


    @Component
    class AutoCallTaskRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<AutoCallTaskRemoteService> {
        protected AutoCallTaskRemoteServiceFallbackFactory() {
            super(true, AutoCallTaskRemoteService.class);
        }
    }
}
