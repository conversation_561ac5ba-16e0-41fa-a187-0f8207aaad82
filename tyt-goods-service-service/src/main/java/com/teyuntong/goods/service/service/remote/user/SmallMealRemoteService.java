package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.goods.service.SmallMealRpcService;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "SmallMealRpcService",
        fallbackFactory = SmallMealRemoteService.SmallMealRemoteFallbackFactory.class)
public interface SmallMealRemoteService extends SmallMealRpcService {

    @Component
    class SmallMealRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SmallMealRemoteService>{
        protected SmallMealRemoteFallbackFactory() {
            super(true, SmallMealRemoteService.class);
        }
    }
}