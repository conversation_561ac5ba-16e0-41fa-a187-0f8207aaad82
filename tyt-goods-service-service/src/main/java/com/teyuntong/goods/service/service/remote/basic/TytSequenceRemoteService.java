package com.teyuntong.goods.service.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.sequence.service.TytSequenceRpcService;
import com.teyuntong.infra.basic.resource.client.source.service.TytSourceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * source测试远程调用
 *
 * <AUTHOR>
 * @since 2024/09/19 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "tytSequenceRemoteService",
        fallbackFactory = TytSequenceRemoteService.TytSequenceRemoteServiceFallback.class)
public interface TytSequenceRemoteService extends TytSequenceRpcService {

    @Component
    class TytSequenceRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<TytSequenceRemoteService> {
        public TytSequenceRemoteServiceFallback() {
            super(true, TytSequenceRemoteService.class);
        }
    }

}
