<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.BackoutReasonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.BackoutReasonDO">
        <id column="id" property="id" />
        <result column="start_point" property="startPoint" />
        <result column="dest_point" property="destPoint" />
        <result column="task_content" property="taskContent" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="user_id" property="userId" />
        <result column="backout_reason_key" property="backoutReasonKey" />
        <result column="backout_reason" property="backoutReason" />
        <result column="status" property="status" />
        <result column="ctime" property="ctime" />
        <result column="specific_reason" property="specificReason" />
        <result column="remark" property="remark" />
        <result column="backout_reason_key_new" property="backoutReasonKeyNew" />
        <result column="backout_reason_new" property="backoutReasonNew" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, start_point, dest_point, task_content, src_msg_id, user_id, backout_reason_key, backout_reason, status, ctime, specific_reason, remark, backout_reason_key_new, backout_reason_new
    </sql>

    <select id="getLastAddMoneyTime" resultType="java.util.Date">
        select ctime
        from tyt_backout_reason
        where backout_reason = '7' and src_msg_id = #{srcMsgId}
        order by ctime desc limit 1
    </select>

    <select id="selectReasonByMsgIds"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.BackoutReasonDO">
        select *
        from tyt_backout_reason
        where status = 1 AND backout_reason in (1,2,3,4,5,6) AND src_msg_id IN
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectALLReasonByMsgIds"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.BackoutReasonDO">
        select *
        from tyt_backout_reason
        where status = 1 AND src_msg_id IN
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTodayUserCancelCount" resultType="java.lang.Integer">
        select count(*)
        from tyt_backout_reason
        where ctime > current_date() and status = 1 and user_id = #{userId}
        <if test="excludeReasonValues != null and excludeReasonValues.size() > 0">
            and backout_reason not in
            <foreach collection="excludeReasonValues" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateStatus">
        UPDATE tyt_backout_reason set status = #{status} WHERE src_msg_id = #{srcMsgId} and status = 1
    </update>
</mapper>
