<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.config.mybatis.mapper.UniversalWordConfigMapper">

    <select id="getConfigByType" resultType="com.teyuntong.goods.service.service.biz.config.mybatis.entity.UniversalWordConfigDO">
        select id, site, code, content, remark, type, delete_status, status, modify_user_id, modify_user_name, ctime, mtime
        from tyt_universal_word_config tuwc
        where delete_status = 0 and status = 1 and type = #{type}
        order by mtime desc
    </select>

    <select id="getByCode" resultType="java.lang.String">
        select content
        from tyt_universal_word_config tuwc
        where delete_status = 0 and status = 1 and code = #{code} limit 1
    </select>
</mapper>
