<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.refresh.mybatis.mapper.GoodsRefreshConfigMapper">

    <select id="selectExcellentGoodsContentByUserId"
            resultType="com.teyuntong.goods.service.client.transport.dto.GoodsRefreshConfigDTO">
        select t.content, u.user_id userId
        from tyt_goods_refresh_user u
                 left join tyt_goods_refresh_config t on u.refresh_code = t.code
        where u.user_id = #{userId}
          and t.config_type = 1
          and (excellent_goods = 1 or instant_grab = 1)
          and t.status = 1
          and t.del = 0
    </select>
</mapper>
