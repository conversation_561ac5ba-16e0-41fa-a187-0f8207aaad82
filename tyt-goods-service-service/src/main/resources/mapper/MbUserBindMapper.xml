<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.MbUserBindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbUserBindDO">
        <id column="id" property="id" />
        <result column="client_user_id" property="clientUserId" />
        <result column="dispatcher_id" property="dispatcherId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, client_user_id, dispatcher_id, status, create_time, update_time
    </sql>

    <select id="getByClientUserId" resultMap="BaseResultMap">
        select *
        from tyt_mb_user_bind
        where client_user_id = #{clientUserId} and status = 1
    </select>

</mapper>
