<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TecServiceFeeV2ConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2ConfigDO">
        <id column="id" property="id" />
        <result column="good_transport_label_type" property="goodTransportLabelType" />
        <result column="good_transport_label" property="goodTransportLabel" />
        <result column="apply_transport_type" property="applyTransportType" />
        <result column="refund_flag_type" property="refundFlagType" />
        <result column="price_publish_type" property="pricePublishType" />
        <result column="free_tec_service_fee_view_count" property="freeTecServiceFeeViewCount" />
        <result column="free_tec_service_fee_call_count" property="freeTecServiceFeeCallCount" />
        <result column="free_tec_service_fee_time" property="freeTecServiceFeeTime" />
        <result column="privacy_phone_type" property="privacyPhoneType" />
        <result column="interests_word" property="interestsWord" />
        <result column="interests_url" property="interestsUrl" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="modify_time" property="modifyTime" />
        <result column="modify_user_id" property="modifyUserId" />
        <result column="modify_user_name" property="modifyUserName" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, good_transport_label_type, good_transport_label, apply_transport_type, refund_flag_type, price_publish_type, free_tec_service_fee_view_count, free_tec_service_fee_call_count, free_tec_service_fee_time, privacy_phone_type, interests_word, interests_url, create_time, create_user_id, create_user_name, modify_time, modify_user_id, modify_user_name, del_status
    </sql>

	<select id="getConfigs"
	        resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2ConfigDO">
        select *
        from tyt_tec_service_fee_v2_config
        where del_status = 0 and apply_transport_type = #{config.applyTransportType}
        and (good_transport_label_type = 1 or good_transport_label = #{config.goodTransportLabel})
        and (refund_flag_type = 0 or refund_flag_type = #{config.refundFlagType})
        and (price_publish_type = 0 or price_publish_type = #{config.pricePublishType})
        limit 1
    </select>

</mapper>
