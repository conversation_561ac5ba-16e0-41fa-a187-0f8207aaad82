<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainMapper">

    <insert id="saveTransportDynamicShowLog" parameterType="list">
        REPLACE INTO tyt_transport_dynamic_show_log (src_msg_id, create_time) values
        <foreach collection="srcMsgIdList" item="srcMsgId" separator=",">
            (#{srcMsgId}, now())
        </foreach>
    </insert>

    <update id="updateCarAgreementAboutTransportMainSomeFieldBySrcMsgId"
            parameterType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        update tyt_transport_main set
        <if test="startPoint !=null">
            start_point = #{startPoint},
        </if>
        <if test="destPoint !=null">
            dest_point = #{destPoint},
        </if>
        <if test="startProvinc !=null">
            start_provinc = #{startProvinc},
        </if>
        <if test="startCity !=null">
            start_city = #{startCity},
        </if>
        <if test="startArea !=null">
            start_area = #{startArea},
        </if>
        <if test="destProvinc !=null">
            dest_provinc = #{destProvinc},
        </if>
        <if test="destCity !=null">
            dest_city = #{destCity},
        </if>
        <if test="destArea !=null">
            dest_area = #{destArea},
        </if>
        <if test="startCoordXValue !=null">
            start_coord_x = #{startCoordXValue},
        </if>
        <if test="startCoordYValue !=null">
            start_coord_y = #{startCoordYValue},
        </if>
        <if test="destCoordXValue !=null">
            dest_coord_x = #{destCoordXValue},
        </if>
        <if test="destCoordYValue !=null">
            dest_coord_y = #{destCoordYValue},
        </if>
        <if test="startDetailAdd !=null">
            start_detail_add = #{startDetailAdd},
        </if>
        <if test="startLongitudeValue !=null">
            start_longitude = #{startLongitudeValue},
        </if>
        <if test="startLatitudeValue !=null">
            start_latitude = #{startLatitudeValue},
        </if>
        <if test="destDetailAdd !=null">
            dest_detail_add = #{destDetailAdd},
        </if>
        <if test="destLongitudeValue !=null">
            dest_longitude = #{destLongitudeValue},
        </if>
        <if test="destLatitudeValue !=null">
            dest_latitude = #{destLatitudeValue},
        </if>
        <if test="beginLoadingTime !=null">
            begin_loading_time = #{beginLoadingTime},
        </if>
        <if test="loadingTime !=null">
            loading_time = #{loadingTime},
        </if>
        <if test="beginUnloadTime !=null">
            begin_unload_time = #{beginUnloadTime},
        </if>
        <if test="unloadTime !=null">
            unload_time = #{unloadTime},
        </if>
        <if test="weight !=null">
            weight = #{weight},
        </if>
        <if test="length !=null">
            length = #{length},
        </if>
        <if test="wide !=null">
            wide = #{wide},
        </if>
        <if test="high !=null">
            high = #{high},
        </if>
        <if test="carLength !=null">
            car_length = #{carLength},
        </if>
        <if test="carType !=null">
            car_type = #{carType},
        </if>
        <if test="carStyle !=null">
            car_style = #{carStyle},
        </if>

        <if test="carMinLength !=null">
            car_min_length = #{carMinLength},
        </if>
        <if test="carMaxLength !=null">
            car_max_length = #{carMaxLength},
        </if>
        <if test="workPlaneMinHigh !=null">
            work_plane_min_high = #{workPlaneMinHigh},
        </if>
        <if test="workPlaneMaxHigh !=null">
            work_plane_max_high = #{workPlaneMaxHigh},
        </if>
        <if test="workPlaneMinLength !=null">
            work_plane_min_length = #{workPlaneMinLength},
        </if>
        <if test="workPlaneMaxLength !=null">
            work_plane_max_length = #{workPlaneMaxLength},
        </if>
        <if test="carLengthLabels !=null">
            car_length_labels = #{carLengthLabels},
        </if>
        <if test="climb !=null">
            climb = #{climb},
        </if>
        <if test="tyreExposedFlag !=null">
            tyre_exposed_flag = #{tyreExposedFlag},
        </if>

        <if test="price !=null">
            price = #{price},
        </if>
        <if test="distance !=null">
            distance = #{distanceValue},
        </if>
        <if test="additionalPrice !=null">
            additional_price = #{additionalPrice},
        </if>
        <if test="enterpriseTaxRate !=null">
            enterprise_tax_rate = #{enterpriseTaxRate},
        </if>
        mtime = now()
        where id = #{srcMsgId};
    </update>
    <update id="noDisplay">
        update tyt_transport_main
        set display_type ='0',
            mtime=now()
        where src_msg_id = #{srcMsgId}
          and display_type = '1'
    </update>

    <update id="saveTransportCreditExposure">
        UPDATE tyt_transport_main
        SET credit_retop = #{creditRetop}
        WHERE src_msg_id = #{srcMsgId}
    </update>

    <update id="invalidTransport">
        UPDATE tyt_transport_main
        SET status = 0,
            mtime  = now()
        WHERE src_msg_id = #{srcMsgId}
          and status = 1
    </update>

    <select id="isHaveTransportMainBySrcMsgIdAndStatus"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from tyt_transport_main
        where id = #{srcMsgId}
    </select>

    <select id="selectEnterpriseTaxRateByUserIdNoStatus" resultType="decimal">
        select enterprise_tax_rate
        from tyt_invoice_enterprise
        where certigier_user_id = #{useId,jdbcType=BIGINT}
        order by id
        limit 1
    </select>

    <select id="getInReleaseInvoiceTransportSrcMsgIdList" resultType="long">
        select src_msg_id
        from tyt_transport
        where status = 1
          and info_status = 0
          and invoice_transport = 1
          and user_id = #{userId}
          and ctime >= #{todayStartDate}
    </select>

    <select id="getInReleaseTransportIdList" resultType="java.lang.Long">
        select src_msg_id
        from tyt_transport_main
        where status = 1
          and user_id = #{userId}
          and ctime >= current_date()
    </select>

    <select id="getInReleaseAndNoPriceTransportSrcMsgIdList" resultType="long">
        select src_msg_id
        from tyt_transport_main
        where status = 1
          and info_status = 0
          and (price is null or price = '0' or price = '')
          and publish_type = 1
          and user_id = #{userId}
          and ctime >= #{todayStartDate}
    </select>

    <select id="getCallCountParamBySrcMsgIdList" resultType="long">
        select tab.src_msg_id from (SELECT src_msg_id, COUNT(DISTINCT t.car_user_id) as count FROM call_phone_record t
        WHERE t.src_msg_id IN
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        GROUP BY t.src_msg_id ) tab where tab.count >= #{callCount} order by tab.count desc
    </select>

    <select id="getViewCountParamBySrcMsgIdList" resultType="long">
        select tab.ts_id from (SELECT ts_id, COUNT(DISTINCT t.user_id) as count FROM tyt_transport_view_log t
        WHERE t.ts_id IN
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        GROUP BY t.ts_id ) tab where tab.count >= #{viewCount} order by tab.count desc
    </select>

    <select id="getInReleaseTransportAndCtimeEarlierSrcMsgIdList" resultType="long">
        select src_msg_id
        from tyt_transport_main where status = 1 and info_status = 0 and ctime &gt;= #{todayStartDate} and ctime &lt;=
        #{earlierDate}
        and src_msg_id in
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        order by ctime desc
    </select>

    <select id="getDynamicCarUserDataList" resultType="long">
        select car_user_id as userId
        from call_phone_record where src_msg_id in
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        union all
        select user_id as userId
        from tyt_transport_view_log where ts_id in
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
    </select>

    <select id="getTodayPublishTransport"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from tyt_transport_main
        where status = 1
          and info_status = 0
          and user_id = #{userId}
          and ctime >= CURDATE()
    </select>

    <select id="getFirstTransport"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from tyt_transport_main FORCE INDEX (IDX_USERID_CTIME)
        where user_id = #{transportUserId}
        order by ctime
        limit 1
    </select>

    <select id="IsHaveGoodCarPriceTransportBySrcMsgIdList" resultType="java.lang.Integer">
        select count(1)
        from tyt_transport_main where id in
        <foreach collection="srcMsgIds" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        and label_json like '%"goodCarPriceTransport":1%'
    </select>

    <select id="getCanceledList"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        SELECT
        *
        FROM
        tyt_transport_main FORCE INDEX (IDX_USERID_CTIME)
        WHERE
        display_type = 1 and source = 0 and is_delete = 0 and status = 5
        and ctime >= #{startTime} and ctime &lt;= #{endTime} and user_id = #{req.userId}
        <if test="req.clientSign == 1">
            and excellent_goods != 1
        </if>
        <if test="req.queryActionType == 1 and req.queryID > 0">
            and id > #{req.queryID}
        </if>
        <if test="req.queryActionType == 2">
            and id &lt; #{req.queryID}
        </if>
        order by mtime desc
        limit #{pageSize}
    </select>

    <select id="getExpiredList"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        SELECT
        *
        FROM
        tyt_transport_main FORCE INDEX (IDX_USERID_CTIME)
        WHERE
        display_type = 1 and source = 0 and is_delete = 0 and status = 1
        and ctime >= #{startTime} and ctime &lt;= #{endTime} and user_id = #{req.userId}
        <if test="req.clientSign == 1">
            and excellent_goods != 1
        </if>
        <if test="req.queryActionType == 1 and req.queryID > 0">
            and id > #{req.queryID}
        </if>
        <if test="req.queryActionType == 2">
            and id &lt; #{req.queryID}
        </if>
        order by id desc
        limit #{pageSize}
    </select>

    <select id="getLastTransportByTransportUserId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select src_msg_id, invoice_transport
        from tyt_transport_main
        where user_id = #{userId}
        order by id desc
        limit 1;
    </select>

    <insert id="initRecordGoodsTransactionInfo">
        insert ignore into tyt_transport_after_order_data (src_msg_id, user_id, task_content, good_type_name,
                                                           start_city, dest_city, distance, weight, price, create_time,
                                                           modify_time)
            value (#{srcMsgId}, #{userId}, #{taskContent}, #{goodTypeName}, #{startCity}, #{destCity}, #{distance},
                   #{weight}, #{price}, now(), now())
    </insert>

    <!-- 获取本人上一票已成交的相同货源 -->
    <select id="getLastSameDealTransport"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from tyt_transport_main force index (IDX_USERID_CTIME)
        where user_id = #{userId}
          and ctime > DATE_SUB(NOW(), INTERVAL 1 MONTH)
          and id != #{srcMsgId}
          and status = 4
          and start_point = #{startPoint}
          and dest_point = #{destPoint}
          and task_content = #{taskContent}
          and length = #{length}
          and wide = #{wide}
          and high = #{high}
          and weight = #{weight}
          and shunting_quantity = #{shuntingQuantity}
          and price > 0
        order by id desc
        limit 1
    </select>

    <select id="hasUserTransportLast30Day" resultType="date">
        SELECT ctime
        FROM tyt_transport_main
        WHERE user_id = #{userId}
          AND ctime between DATE_SUB(CURDATE(), INTERVAL 30 DAY) and CURDATE()
        order by id desc
        LIMIT 1
    </select>

    <select id="selectOfPublishType" resultType="java.lang.String">
        select price
        from tyt_transport_main
        where user_id = #{userId}
          and ctime >= #{publishTime}
          and is_delete = 0
          and price > 0
        limit 1
    </select>

    <select id="getUserSomeDayTransportData"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        SELECT src_msg_id, `status`, publish_type
        FROM tyt_transport_main
        WHERE user_id = #{userId}
          AND ctime between CONCAT(#{someDay}, ' 00:00:00') and CONCAT(#{someDay}, ' 23:59:59')
    </select>

    <select id="getPublishCountByUserId" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_main
        where user_id = #{userId}
          and ctime between #{startTime} and #{endTime}
    </select>

    <select id="getUserCommissionGoods"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select src_msg_id   as srcMsgId,
               start_city   as startCity,
               dest_city    as destCity,
               task_content as taskContent,
               price
        from tyt_transport_main
        where user_id = #{userId}
          and ctime >= CONCAT(CURDATE(), ' 00:00:00')
          and status = 1
          and tec_service_fee > 0
        order by id desc
        limit 30
    </select>

    <select id="countDuplicateTransport" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_main
        where hash_code = #{hashCode}
          and user_id = #{userId}
          and ctime > #{startTime}
          and (FIND_IN_SET(tel, #{telsStr}) or FIND_IN_SET(tel3, #{telsStr}) or FIND_IN_SET(tel4, #{telsStr}))
          and status = 1
    </select>

    <select id="selectDistanceBySrcMsgId" resultType="java.lang.Integer">
        select distance
        from tyt_transport_main
        where id = #{srcMsgId}
    </select>

    <select id="getFirstSimilarityGoods"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from tyt_transport_main
        where similarity_code = #{similarityCode}
        limit 1
    </select>

    <select id="getUserDayPublishCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tyt_transport
        WHERE ctime >= current_date()
          and status = 1
          and user_id = #{userId}
    </select>

    <select id="getSameCityTransportAllCount"
            resultType="integer">
        select count(1)
        from tyt_transport_main where start_city = #{startCity} and dest_city = #{destCity}
        and ctime BETWEEN #{startTime} AND #{endTime}
        <if test="type == 1">
            and publish_type = 1 and price = ''
        </if>
        <if test="type == 2">
            and publish_type = 1 and price is not null and price != ''
        </if>
        <if test="type == 3">
            and publish_type = 2
        </if>
    </select>

    <select id="getSameCityTransportBargainCont"
            resultType="integer">
        select count(1)
        from tyt_transport_main where start_city = #{startCity} and dest_city = #{destCity}
        and ctime BETWEEN #{startTime} AND #{endTime}
        <if test="type == 1">
            and publish_type = 1 and price = ''
        </if>
        <if test="type == 2">
            and publish_type = 1 and price is not null and price != ''
        </if>
        <if test="type == 3">
            and publish_type = 2
        </if>
        and status = 4
    </select>
    <select id="getTransportCountForUserId" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_main where user_id in
        <foreach collection="transportCountDTO.userIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="transportCountDTO.startTime != null">
            and ctime >= #{transportCountDTO.startTime}
        </if>
        <if test="transportCountDTO.endTime != null">
            and ctime &lt;= #{transportCountDTO.endTime}
        </if>
        <if test="transportCountDTO.status != null">
            and status = #{transportCountDTO.status}
        </if>
        <if test="transportCountDTO.publishType != null">
            and publish_type = #{transportCountDTO.publishType}
        </if>
        <if test="transportCountDTO.refundFlag != null">
            and refund_flag = #{transportCountDTO.refundFlag}
        </if>
        <if test="transportCountDTO.excellentGoods != null">
            and excellent_goods = #{transportCountDTO.excellentGoods}
        </if>

    </select>

    <select id="getLastTransport"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from `tyt_transport_main`
        where user_id = #{userId}
        order by ctime desc
        limit 1
    </select>

    <!-- 修改display_type，使货源隐藏 -->
    <update id="updateDisplayTypeToHide">
        update tyt_transport_main
        set display_type = 0,
            mtime        = now()
        where src_msg_id = #{srcMsgId}
    </update>
    <select id="getHistoryIdListBySrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where user_id = #{userId}
          and src_msg_id = #{srcMsgId}
          and display_type = 1
          and ctime &lt; #{todayStartDate}
    </select>

    <select id="countSimilarityGoods" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_main
        where ctime > current_date
          and status = 1
          and similarity_code = #{similarityCode}
          and id != #{srcMsgId}
    </select>

    <select id="getInReleaseTransport" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where status = 1 and ctime > current_date() and id in
        <foreach collection="srcMsgIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="countSameRouteGoods" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_main
        where ctime > current_date
          and status = 1
          and start_city = #{startCity}
          and dest_city = #{destCity}
          and id != #{srcMsgId}
    </select>

    <update id="updateDisplayTypeByIds">
        update tyt_transport_main set display_type = #{display} where id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateLabelJsonBySrcMsgId">
        UPDATE tyt_transport_main
        SET label_json = #{labelJson},
            mtime      = NOW()
        WHERE src_msg_id = #{srcMsgId}
    </update>

    <update id="updateStatusById">
        update tyt_transport_main set status = #{status}, display_type = #{display}, mtime = now(), is_display = 1
        <if test="infoStatus != null and infoStatus != ''">
            , info_status = #{infoStatus}
        </if>
        where src_msg_id = #{srcMsgId}
    </update>

    <update id="updateTransportMain">
        UPDATE tyt_transport_main
        SET start_point                    = #{transportMain.startPoint},
            dest_point                     = #{transportMain.destPoint},
            task_content                   = #{transportMain.taskContent},
            tel                            = #{transportMain.tel},
            pub_time                       = #{transportMain.pubTime},
            pub_qq                         = #{transportMain.pubQq},
            nick_name                      = #{transportMain.nickName},
            user_show_name                 = #{transportMain.userShowName},
            `status`                       = #{transportMain.status},
            source                         = #{transportMain.source},
            ctime                          = #{transportMain.ctime},
            mtime                          = #{transportMain.mtime},
            upload_cellphone               = #{transportMain.uploadCellphone},
            resend                         = #{transportMain.resend},
            start_coord                    = #{transportMain.startCoord},
            dest_coord                     = #{transportMain.destCoord},
            plat_id                        = #{transportMain.platId},
            verify_flag                    = #{transportMain.verifyFlag},
            price                          = #{transportMain.price},
            user_id                        = #{transportMain.userId},
            price_code                     = #{transportMain.priceCode},
            start_coord_x                  = #{transportMain.startCoordXValue},
            start_coord_y                  = #{transportMain.startCoordYValue},
            dest_coord_x                   = #{transportMain.destCoordXValue},
            dest_coord_y                   = #{transportMain.destCoordYValue},
            start_detail_add               = #{transportMain.startDetailAdd},
            start_longitude                = #{transportMain.startLongitudeValue},
            start_latitude                 = #{transportMain.startLatitudeValue},
            dest_detail_add                = #{transportMain.destDetailAdd},
            dest_longitude                 = #{transportMain.destLongitudeValue},
            dest_latitude                  = #{transportMain.destLatitudeValue},
            pub_date                       = #{transportMain.pubDate},
            goods_code                     = #{transportMain.goodsCode},
            weight_code                    = #{transportMain.weightCode},
            weight                         = #{transportMain.weight},
            length                         = #{transportMain.length},
            wide                           = #{transportMain.wide},
            high                           = #{transportMain.high},
            is_superelevation              = #{transportMain.isSuperelevation},
            linkman                        = #{transportMain.linkman},
            remark                         = #{transportMain.remark},
            distance                       = #{transportMain.distanceValue},
            pub_goods_time                 = #{transportMain.pubGoodsTime},
            tel3                           = #{transportMain.tel3},
            tel4                           = #{transportMain.tel4},
            display_type                   = #{transportMain.displayType },
            hash_code                      = #{transportMain.hashCode},
            is_car                         = #{transportMain.isCar},
            user_type                      = #{transportMain.userType},
            pc_old_content                 = #{transportMain.pcOldContent},
            resend_counts                  = #{transportMain.resendCounts},
            verify_photo_sign              = #{transportMain.verifyPhotoSign},
            user_part                      = #{transportMain.userPart},
            start_city                     = #{transportMain.startCity},
            src_msg_id                     = #{transportMain.srcMsgId},
            start_provinc                  = #{transportMain.startProvinc},
            start_area                     = #{transportMain.startArea},
            dest_provinc                   = #{transportMain.destProvinc},
            dest_city                      = #{transportMain.destCity},
            dest_area                      = #{transportMain.destArea},
            client_version                 = #{transportMain.clientVersion},
            is_info_fee                    = #{transportMain.isInfoFee},
            info_status                    = #{transportMain.infoStatus},
            ts_order_no                    = #{transportMain.tsOrderNo},
            release_time                   = #{transportMain.releaseTime},
            reg_time                       = #{transportMain.regTime},
            type                           = #{transportMain.type},
            brand                          = #{transportMain.brand},
            good_type_name                 = #{transportMain.goodTypeName},
            good_number                    = #{transportMain.goodNumber},
            is_standard                    = #{transportMain.isStandard},
            match_item_id                  = #{transportMain.matchItemId},
            android_distance               = #{transportMain.androidDistanceValue},
            ios_distance                   = #{transportMain.iosDistanceValue},
            is_display                     = #{transportMain.isDisplay},
            refer_length                   = #{transportMain.referLength},
            refer_width                    = #{transportMain.referWidth},
            refer_height                   = #{transportMain.referHeight},
            refer_weight                   = #{transportMain.referWeight},
            car_length                     = #{transportMain.carLength},
            loading_time                   = #{transportMain.loadingTime},
            begin_unload_time              = #{transportMain.beginUnloadTime},
            unload_time                    = #{transportMain.unloadTime},
            car_min_length                 = #{transportMain.carMinLength},
            car_max_length                 = #{transportMain.carMaxLength},
            car_type                       = #{transportMain.carType},
            begin_loading_time             = #{transportMain.beginLoadingTime},
            car_style                      = #{transportMain.carStyle},
            work_plane_min_high            = #{transportMain.workPlaneMinHigh},
            work_plane_max_high            = #{transportMain.workPlaneMaxHigh},
            work_plane_min_length          = #{transportMain.workPlaneMinLength},
            work_plane_max_length          = #{transportMain.workPlaneMaxLength},
            climb                          = #{transportMain.climb},
            order_number                   = #{transportMain.orderNumber},
            evaluate                       = #{transportMain.evaluate},
            special_required               = #{transportMain.specialRequired},
            similarity_code                = #{transportMain.similarityCode},
            similarity_first_id            = #{transportMain.similarityFirstId},
            similarity_first_info          = #{transportMain.similarityFirstInfo},
            tyre_exposed_flag              = #{transportMain.tyreExposedFlag},
            car_length_labels              = #{transportMain.carLengthLabels},
            shunting_quantity              = #{transportMain.shuntingQuantity},
            first_publish_type             = #{transportMain.firstPublishType},
            publish_type                   = #{transportMain.publishType},
            info_fee                       = #{transportMain.infoFee},
            exclusive_type                 = #{transportMain.exclusiveType},
            is_delete                      = #{transportMain.isDelete},
            total_score                    = #{transportMain.totalScore},
            rank_level                     = #{transportMain.rankLevel},
            is_show                        = #{transportMain.isShow},
            refund_flag                    = #{transportMain.refundFlag},
            source_type                    = #{transportMain.sourceType},
            trade_num                      = #{transportMain.tradeNum},
            auth_name                      = #{transportMain.authName},
            label_json                     = #{transportMain.labelJson},
            guarantee_goods                = #{transportMain.guaranteeGoods},
            credit_retop                   = #{transportMain.creditRetop},
            sort_type                      = #{transportMain.sortType},
            priority_recommend_expire_time = #{transportMain.priorityRecommendExpireTime},
            excellent_goods                = #{transportMain.excellentGoods},
            excellent_goods_two            = #{transportMain.excellentGoodsTwo},
            driver_driving                 = #{transportMain.driverDriving},
            load_cell_phone                = #{transportMain.loadCellPhone},
            unload_cell_phone              = #{transportMain.unloadCellPhone},
            cargo_owner_id                 = #{transportMain.cargoOwnerId},
            tec_service_fee                = #{transportMain.tecServiceFee},
            machine_remark                 = #{transportMain.machineRemark},
            excellent_card_id              = #{transportMain.excellentCardId},
            invoice_transport              = #{transportMain.invoiceTransport},
            additional_price               = #{transportMain.additionalPrice},
            enterprise_tax_rate            = #{transportMain.enterpriseTaxRate}
        WHERE id = #{transportMain.id};
    </update>

    <select id="getTodayIdListBySrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where user_id = #{userId}
          and src_msg_id = #{srcMsgId}
          and status = #{status}
          and ctime >= CURDATE()
    </select>
    <select id="getSimilarityForOrderCancelPush"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO">
        select *
        from tyt_transport_main
        where status = 1
        and start_city = #{transportMain.startCity}
        and dest_city = #{transportMain.destCity}
        <if test="transportMain.goodTypeName != null and transportMain.goodTypeName != '' ">
            and good_type_name = #{transportMain.goodTypeName}
        </if>
        and weight &gt; #{transportMain.weight} * 0.9
        and weight &lt; #{transportMain.weight} * 1.1
        and ctime >= CURDATE()
        <if test="excludeSrcMsgIds != null and excludeSrcMsgIds.size() > 0">
            and src_msg_id not in
            <foreach collection="excludeSrcMsgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
    </select>
    <select id="getTransportForUser" resultType="java.lang.Long">
        select id from tyt_transport_main where user_id = #{userId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="startTime != null">
            and ctime &gt; #{startTime}
        </if>
        <if test="endTime != null">
            and ctime &lt; #{endTime}
        </if>
    </select>

    <select id="getSimilarityGoodsTopPrice" resultType="java.lang.String">
        select price from tyt_transport_main where similarity_code = #{similarityCode}
        and price is not null and price != '' and price != '0' order by price desc limit 1
    </select>

    <select id="getByGoodsPoint" resultType="com.teyuntong.goods.service.client.publish.vo.CarouselGoodsVO">
        select start_provinc as startProvince, start_city as startCity, start_area as startArea, dest_provinc as destProvince,
        dest_city as destCity, dest_area as destArea, user_show_name as nickName from tyt_transport_main force index(INDEX_CTIME) where ctime > current_date
        and status = 1
        <if test="carouselGoodsDTO.startProvince != null and carouselGoodsDTO.startProvince != ''">
            and start_provinc = #{carouselGoodsDTO.startProvince}
        </if>
        <if test="carouselGoodsDTO.startCity != null and carouselGoodsDTO.startCity != ''">
            and start_city = #{carouselGoodsDTO.startCity}
        </if>
        <if test="carouselGoodsDTO.startArea != null and carouselGoodsDTO.startArea != ''">
            and start_area = #{carouselGoodsDTO.startArea}
        </if>
        <if test="carouselGoodsDTO.destProvince != null and carouselGoodsDTO.destProvince != ''">
            and dest_provinc = #{carouselGoodsDTO.destProvince}
        </if>
        <if test="carouselGoodsDTO.destCity != null and carouselGoodsDTO.destCity != ''">
            and dest_city = #{carouselGoodsDTO.destCity}
        </if>
        <if test="carouselGoodsDTO.destArea != null and carouselGoodsDTO.destArea != ''">
            and dest_area = #{carouselGoodsDTO.destArea}
        </if>
        order by id desc limit 30
    </select>

</mapper>
