<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportDispatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDispatchDO">
        <id column="id" property="id"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="user_id" property="userId"/>
        <result column="publish_user_id" property="publishUserId"/>
        <result column="publish_user_name" property="publishUserName"/>
        <result column="dispatcher_id" property="dispatcherId"/>
        <result column="dispatcher_name" property="dispatcherName"/>
        <result column="owner_freight" property="ownerFreight"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="give_goods_phone" property="giveGoodsPhone"/>
        <result column="give_goods_name" property="giveGoodsName"/>
        <result column="publish_platform" property="publishPlatform"/>
        <result column="info_fee_diff" property="infoFeeDiff"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, user_id, publish_user_id, publish_user_name, dispatcher_id, dispatcher_name, owner_freight, create_time, modify_time, give_goods_phone, give_goods_name, publish_platform, info_fee_diff
    </sql>


    <!-- 查询货源代调表信息 -->
    <select id="selectOne"
            parameterType="com.teyuntong.goods.service.client.transport.dto.TransportDispatchDTO"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDispatchDO">
        select
        <include refid="Base_Column_List"/>
        from tyt_transport_dispatch
        where src_msg_id = #{srcMsgId}
        limit 1
    </select>

	<select id="getBySrcMsgId" resultMap="BaseResultMap">
        select *
        from tyt_transport_dispatch
        where src_msg_id = #{srcMsgId}
        limit 1
    </select>

    <!-- 返回首次成交的货源id -->
    <select id="getFirstDealTransportIdByGivenPhone" resultType="long">
        SELECT m.id
        FROM tyt_transport_main m
        JOIN tyt_transport_dispatch d ON m.id = d.src_msg_id
        WHERE d.give_goods_phone = #{giveGoodsPhone}
        AND m.status = 4
        LIMIT 1
    </select>

    <select id="getDispatchAndGoodsInfo" resultType="com.teyuntong.goods.service.service.biz.transport.dto.TytTransportDTO">
        select ttd.dispatcher_id  as dispatchId,
               ttd.src_msg_id     as srcMsgId,
               ttm.start_point    as startPoint,
               ttm.dest_point     as destPoint,
               ttm.task_content   as taskContent,
               ttm.user_show_name as userShowName
        from tyt_transport_dispatch ttd
                 left join tyt_transport_main ttm on ttd.src_msg_id = ttm.src_msg_id
        where ttd.src_msg_id = #{srcMsgId}
    </select>
</mapper>
