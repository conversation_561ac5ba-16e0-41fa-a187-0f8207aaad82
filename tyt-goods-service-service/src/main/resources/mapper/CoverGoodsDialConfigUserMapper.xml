<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.cover.mybatis.mapper.CoverGoodsDialConfigUserMapper">

	<select id="selectEnabledLastImported"
	        resultType="com.teyuntong.goods.service.service.biz.cover.mybatis.entity.CoverGoodsDialConfigUserDO">
		select confUser.*
		from tyt_cover_goods_dial_config_user confUser
			     left join tyt_cover_goods_dial_config conf on confUser.dial_config_id = conf.id
		where conf.enable = 1
		  and confUser.user_id = #{userId}
		  and conf.del_flag = 0
		order by confUser.update_time DESC
		limit 1
	</select>
</mapper>
