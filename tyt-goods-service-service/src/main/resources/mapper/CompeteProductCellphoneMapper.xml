<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.CompeteProductCellphoneMapper">

    <select id="getCompeteCellPhone" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `compete_product_cellphone`
        WHERE status = 1 and cell_phone IN
        <foreach collection="tels" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
