<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.publish.mybatis.mapper.TransportAutoResendRecordMapper">

    <!-- 返回当前已自动重发次数，不包含重发失败 -->
    <select id="getResendTimes" resultType="integer">
        select count(*) from tyt_transport_auto_resend_record
        where user_id = #{userId}
        and create_time between #{startTime} and #{endTime}
        and status != 2
    </select>

    <select id="getRecordByOldSrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportAutoResendRecordDO">
        select *
        from tyt_transport_auto_resend_record
        where old_src_msg_id = #{oldSrcMsgId}
        order by id desc limit 1
    </select>


</mapper>
