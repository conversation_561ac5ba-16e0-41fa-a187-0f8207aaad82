<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.ExcellentPriceCountGainMapper">

    <!-- 统计用户有效期内的总次数和使用次数 -->
    <select id="sumValidByUser"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ExcellentPriceCountGainDO">
        select
            user_id as userId,
            sum(gain_count) as gainCount,
            sum(used_count) as usedCount
        from tyt_excellent_price_count_gain
        where user_id in ( <foreach item="item" collection="userIds" separator=",">#{item}</foreach> )
        and status = 1
        group by user_id
    </select>

    <!-- 查询有效的发放列表，状态有效，包含剩余次数=0的数据 -->
    <select id="validGainList"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ExcellentPriceCountGainDO">
        select * from tyt_excellent_price_count_gain
        where user_id = #{userId}
        and status = 1
        order by expire_date_end, id
    </select>

</mapper>
