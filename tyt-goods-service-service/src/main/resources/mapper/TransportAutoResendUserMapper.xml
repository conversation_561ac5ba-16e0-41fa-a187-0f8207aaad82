<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.publish.mybatis.mapper.TransportAutoResendUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportAutoResendUserDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="limit_num" property="limitNum"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, limit_num, create_name, create_time, modify_name, modify_time
    </sql>

    <select id="getByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_transport_auto_resend_user
        where user_id = #{userId}
    </select>
</mapper>
