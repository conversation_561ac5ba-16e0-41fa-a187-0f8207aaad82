<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.recommend.mybatis.mapper.ExcellentGoodsPriceConfigMapper">


    <select id="getConfig"
            resultType="com.teyuntong.goods.service.service.biz.recommend.mybatis.entity.ExcellentGoodsPriceConfigDO">
        select * from excellent_goods_label_config_route_data
        where start_city = #{startCity}
        and dest_city = #{destCity}
        and #{goodsWeight} >= weight_min
        and weight_max > #{goodsWeight}
        and #{distance} >= distance_min
        and distance_max > #{distance}
        limit 1
    </select>
</mapper>
