<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SigningCarInfoMapper">

    <select id="getCountBySigningIdAndPhone" resultType="java.lang.Integer">
        select count(*)
        from tyt_signing_car_info
        where signing_id = #{signingId}
          and phone = #{phone}
    </select>

    <update id="updateAssignNum">
        update tyt_signing_car_info
        set assign_success_num = assign_success_num + 1,
            update_time        = now()
        where signing_id = #{signingId}
          and phone = #{phone}
    </update>

    <select id="getByIdName" resultType="java.lang.Integer">
        select count(*) from tyt_signing_car_info where signing_id = #{id}  and phone = #{phone}
    </select>
</mapper>
