<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportEnterpriseConfigLogMapper">

    <select id="getLastByEnterpriseId" resultType="com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportEnterpriseConfigLogDO">
        select *
        from tyt_invoice_transport_enterprise_config_log
        where enterprise_id = #{enterpriseId}
        order by id desc
        limit 1
    </select>
</mapper>
