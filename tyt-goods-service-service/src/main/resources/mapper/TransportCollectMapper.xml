<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportCollectMapper">

    <select id="isExit"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportCollectDO">
        select *
        from tyt_transport_collect
        where user_id = #{userId} and info_id = #{srcMsgId} and STATUS = 1
    </select>
</mapper>
