<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.MbCargoSyncLogMapper">

    <select id="selectLastVersionJson"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbCargoSyncLogDO">
        select
            cargo_id cargoId,
            cargo_version cargoVersion,
            info_json infoJson
        from tyt_mb_cargo_sync_log
        where cargo_id = #{cargoId} and sync_type != 2 order by cargo_version desc limit 1
    </select>
</mapper>
