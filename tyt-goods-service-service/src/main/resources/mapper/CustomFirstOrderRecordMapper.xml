<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper.CustomFirstOrderRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.CustomFirstOrderRecordDO">
        <id column="id" property="id"/>
        <result column="custom_phone" property="customPhone"/>
        <result column="first_publish_time" property="firstPublishTime"/>
        <result column="first_finish_order_time" property="firstFinishOrderTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, custom_phone, first_publish_time, first_finish_order_time, status
    </sql>

    <select id="getByCustomPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_custom_first_order_record
        where custom_phone = #{customPhone}
    </select>

    <update id="updateFirstFinishOrderTimeByCustomPhone">
        update tyt_custom_first_order_record
        set first_finish_order_time = #{firstFinishOrderTime}
        where custom_phone = #{customPhone}
    </update>

    <select id="countFinishOrder" resultType="java.lang.Integer">
        select count(1)
        from tyt_custom_first_order_record
        where custom_phone = #{cellPhone}
          and first_finish_order_time is not null
    </select>
</mapper>
