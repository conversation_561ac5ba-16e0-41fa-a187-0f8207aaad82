<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.MbCargoSyncInfoMapper">


    <select id="selectBySrcMsgId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbCargoSyncInfoDO">
        SELECT
            i.*
        FROM
            tyt_transport_mb_merge m
                LEFT JOIN
            tyt_mb_cargo_sync_info i
            ON
                m.cargo_id = i.cargo_id
        WHERE
            m.src_msg_id = #{srcMsgId}
          AND m.STATUS = 0
          AND i.del_flag = 0
          AND i.last_operate_action IN ( 0, 1 )
        ORDER BY
            i.update_time DESC
            LIMIT 1;
    </select>

    <select id="getByCargoId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbCargoSyncInfoDO">
        select *
        from tyt_mb_cargo_sync_info
        where cargo_id = #{cargoId}
    </select>
    <select id="selectByCargoId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbCargoSyncInfoDO">
        select *
        from tyt_mb_cargo_sync_info
        where cargo_id = #{cargoId}
    </select>

</mapper>