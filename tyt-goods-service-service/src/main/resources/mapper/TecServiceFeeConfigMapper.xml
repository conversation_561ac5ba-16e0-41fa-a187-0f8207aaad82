<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TecServiceFeeConfigMapper">

    <select id="getConfigs" resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeConfigDO">
        SELECT *
        FROM tyt_tec_service_fee_config
        WHERE
            apply_transport_type = #{applyTransportType}
            <if test="applyTransportType == 1">
                AND special_carcooperative_type = #{specialCarCooperativeType}
            </if>
            AND (refund_flag_type = #{refundFlagType} or refund_flag_type = 0)
            AND (price_publish_type = #{pricePublishType} or price_publish_type = 0)
        LIMIT 1
    </select>

</mapper>
