<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytUserempowerSyncgoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytUserempowerSyncgoodsDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="goods_user_name" property="goodsUserName" />
        <result column="status" property="status" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="sync_time" property="syncTime" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, goods_user_name, status, update_user_id, update_user_name, sync_time, ctime, mtime, is_delete
    </sql>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tyt_userempower_syncgoods
        where is_delete = 0 and user_id = #{userId} limit 1
    </select>

    <update id="updateAuthStatus">
        update tyt_userempower_syncgoods
        set status = 0,
            update_user_id = null,
            update_user_name = '用户授权',
            sync_time = now(),
            mtime = now()
        where user_id = #{userId} and is_delete = 0
    </update>

    <insert id="insertAuthRecord">
        insert into tyt_userempower_syncgoods (
            user_id,
            goods_user_name,
            status,
            update_user_id,
            update_user_name,
            sync_time,
            ctime,
            mtime,
            is_delete
        ) values (
            #{userId},
            #{goodsUserName},
            0,
            null,
            '用户授权',
            now(),
            now(),
            now(),
            0
        )
    </insert>
</mapper>
