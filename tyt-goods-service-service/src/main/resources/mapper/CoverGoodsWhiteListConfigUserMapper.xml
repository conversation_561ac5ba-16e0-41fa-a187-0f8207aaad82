<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.cover.mybatis.mapper.CoverGoodsWhiteListConfigUserMapper">

	<select id="countByUserId" resultType="java.lang.Long">
		select count(*)
		from tyt_cover_goods_white_list_config_user whiteUser
			     left join tyt_cover_goods_dial_config conf on whiteUser.dial_config_id = conf.id
		where whiteUser.user_id = #{userId}
		  and conf.del_flag = 0
		  and conf.enable = 1
	</select>
</mapper>
