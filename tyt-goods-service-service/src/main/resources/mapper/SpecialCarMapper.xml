<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="tyt_cell_phone" property="tytCellPhone" />
        <result column="name" property="name" />
        <result column="driver_id" property="driverId" />
        <result column="driver_user_id" property="driverUserId" />
        <result column="driver_phone" property="driverPhone" />
        <result column="phone" property="phone" />
        <result column="driving_ability" property="drivingAbility" />
        <result column="city" property="city" />
        <result column="car_id" property="carId" />
        <result column="car_type" property="carType" />
        <result column="head_city_no" property="headCityNo" />
        <result column="tail_city_no" property="tailCityNo" />
        <result column="length" property="length" />
        <result column="table_height" property="tableHeight" />
        <result column="other_pure_flat" property="otherPureFlat" />
        <result column="ladder_type" property="ladderType" />
        <result column="remark" property="remark" />
        <result column="fraction" property="fraction" />
        <result column="status" property="status" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="create_name" property="createName" />
        <result column="update_name" property="updateName" />
        <result column="distance_preference" property="distancePreference" />
        <result column="bi_distance" property="biDistance" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, tyt_cell_phone, name, driver_id, driver_user_id, driver_phone, phone, driving_ability, city, car_id, car_type, head_city_no, tail_city_no, length, table_height, other_pure_flat, ladder_type, remark, fraction, status, reason, create_time, modify_time, create_name, update_name, distance_preference, bi_distance
    </sql>

</mapper>
