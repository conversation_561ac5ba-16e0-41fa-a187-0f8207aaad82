<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.DrawCommissionRuleMapper">

    <select id="selectRule" resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.DrawCommissionRuleDO">
        select *
        from tyt_draw_commission_rule
        where enabled = 1
        and (commission_source = #{commissionSource} or commission_source= -1)
        and (publish_type = #{publishType} or publish_type = -1)
        and (have_price = #{havePrice} or have_price = -1)
        and (refund_flag = #{refundFlag} or refund_flag = -1)
        and start_date &lt;= #{commissionTime}
        and end_date &gt;= #{commissionTime}
        and (
        (good_transport = 1 and #{goodsModelScore} BETWEEN min_fractional_line AND max_fractional_line)
        or good_transport != 1
        )
        and (good_transport_label is null or good_transport_label = '' or good_transport_label like concat('%',#{goodTransportTabel},'%'))
        order by priority_grade, modify_time desc limit 1
    </select>
</mapper>
