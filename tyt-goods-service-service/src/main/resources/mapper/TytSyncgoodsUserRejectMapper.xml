<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytSyncgoodsUserRejectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytSyncgoodsUserRejectDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, create_time
    </sql>

    <insert id="insertRejectRecord">
        insert into tyt_syncgoods_user_reject (
            user_id, create_time
        ) values (
            #{userId},
            now()
        )
    </insert>

    <select id="countRejectByUserId" resultType="java.lang.Integer">
        select count(1)
        from tyt_syncgoods_user_reject
        where user_id = #{userId}
    </select>

    <delete id="cleanAllByUserId">
        delete from tyt_syncgoods_user_reject where user_id = #{userId}
    </delete>
</mapper>
