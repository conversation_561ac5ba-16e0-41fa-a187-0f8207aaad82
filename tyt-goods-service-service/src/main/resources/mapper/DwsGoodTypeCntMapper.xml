<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.archive.mapper.DwsGoodTypeCntMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.archive.entity.DwsGoodTypeCnt">
        <id column="user_id" property="userId" />
        <id column="good_type_name" property="goodTypeName" />
        <result column="cnt" property="cnt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, good_type_name, cnt
    </sql>

	<select id="selectCntByGoodTypeName" resultType="java.lang.Integer">
        select cnt
        from dws_good_type_cnt
        where user_id = #{carUserId}
        <if test="goodTypeName != null and goodTypeName !=''">
            and good_type_name = #{goodTypeName}
        </if>
    </select>

    <select id="getTopGoodTypeNames" resultMap="BaseResultMap">
        select *
        from dws_good_type_cnt
        where user_id = #{carUserId}
        <if test="goodTypeName != null and goodTypeName !=''">
            and good_type_name != #{goodTypeName}
        </if>
        order by cnt desc limit #{limitNum}
    </select>

    <select id="getGoodsTypeCnt" resultType="java.lang.Integer">
        select cnt
        from dws_good_type_cnt
        where user_id = #{carUserId} and good_type_name = #{goodTypeName}
    </select>

</mapper>
