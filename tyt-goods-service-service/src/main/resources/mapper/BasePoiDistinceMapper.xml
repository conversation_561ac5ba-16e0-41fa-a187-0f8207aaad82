<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.BasePoiDistinceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.BasePoiDistinceDO">
        <result column="id" property="id" />
        <result column="poi_city" property="poiCity" />
        <result column="poi_area" property="poiArea" />
        <result column="warehouse" property="warehouse" />
        <result column="distinct" property="distinct" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, poi_city, poi_area, warehouse, distinct
    </sql>

</mapper>
