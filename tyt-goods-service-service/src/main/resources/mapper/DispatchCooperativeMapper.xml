<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.DispatchCooperativeMapper">


    <select id="selectByName"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO">
        select *
        from tyt_dispatch_cooperative
        where cooperative_name = #{name}
          and status = 1
          and delete_flag = 0
        limit 1
    </select>

    <select id="selectListByName" resultType="com.teyuntong.goods.service.client.publish.vo.CooperativeVO">
        select id,
               cooperative_name as cooperativeName
        from tyt_dispatch_cooperative
        where status = 1 and delete_flag = 0
        <if test="name != null and name != ''">
            and cooperative_name like concat("%",#{name},"%")
        </if>
    </select>

    <select id="selectPlatId" resultType="java.lang.Long">
        select id
        from tyt_dispatch_cooperative
        where status = 1 and delete_flag = 0 and cooperative_name = "平台"
    </select>
</mapper>
