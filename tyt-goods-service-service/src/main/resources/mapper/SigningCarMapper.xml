<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SigningCarMapper">

	<select id="getAssignableCarsByRoute"
	        resultType="com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarInfoDTO">
		select info.id as carInfoId,
		       info.driving_ability as drivingAbility,
		       info.head_city_no AS headCityNo,
		       info.tail_city_no AS tailCityNo,
		       info.car_type as carType,
		       car.signing as signing,
		       car.user_id as userId,
		       car.tyt_cell_phone as phone,
		       driver.verify_status as verifyStatus,
		       info.driver_id as driverId,
		       info.driver_user_id as driverUserId,
		       info.distance_preference as distancePreference,
		       info.driver_tag as driverTag,
		       info.favorable_comment AS favorableComment,
		       info.receiving_orders AS receivingOrders
		from tyt_signing_car_info info
	     left join tyt_invoice_driver driver on info.driver_id = driver.id
	     left join tyt_signing_car car on info.signing_id = car.id
	     left join tyt_signing_car_info_route route on info.id = route.car_info_id
		where car.status = 1 and route.start_city = #{startCity} and route.dest_city = #{destCity}
	</select>

	<select id="getAssignableCarsByCity"
	        resultType="com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarInfoDTO">
		select info.id as carInfoId,
				info.driving_ability as drivingAbility,
				info.head_city_no AS headCityNo,
				info.tail_city_no AS tailCityNo,
				info.car_type as carType,
				car.signing as signing,
				car.user_id as userId,
				car.tyt_cell_phone as phone,
				driver.verify_status as verifyStatus,
				info.driver_id as driverId,
				info.driver_user_id as driverUserId,
				info.distance_preference as distancePreference,
				info.driver_tag as driverTag,
				info.favorable_comment AS favorableComment,
				info.receiving_orders AS receivingOrders
		from tyt_signing_car_info info
		left join tyt_invoice_driver driver on info.driver_id = driver.id
		left join tyt_signing_car car on info.signing_id = car.id
		left join tyt_signing_car_info_city city on info.id = city.car_info_id
		where car.status = 1 and city.type = 2 and city.city in
		<foreach collection="cities" item="city" index="index" open="(" close=")" separator=",">
			#{city}
		</foreach>
	</select>

	<select id="getAssignableCarsByProvince"
	        resultType="com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarInfoDTO">
		select info.id as carInfoId,
				info.driving_ability as drivingAbility,
				info.head_city_no AS headCityNo,
				info.tail_city_no AS tailCityNo,
				info.car_type as carType,
				car.signing as signing,
				car.user_id as userId,
				car.tyt_cell_phone as phone,
				driver.verify_status as verifyStatus,
				info.driver_id as driverId,
				info.driver_user_id as driverUserId,
				info.distance_preference as distancePreference,
				info.driver_tag as driverTag,
				info.favorable_comment AS favorableComment,
				info.receiving_orders AS receivingOrders
		from tyt_signing_car_info info
		left join tyt_invoice_driver driver on info.driver_id = driver.id
		left join tyt_signing_car car on info.signing_id = car.id
		left join tyt_signing_car_info_city city on info.id = city.car_info_id
		where car.status = 1 and city.type = 1 and city.city in
		<foreach collection="provinces" item="province" index="index" open="(" close=")" separator=",">
			#{province}
		</foreach>
	</select>

	<select id="getAssignableCarsByCountry"
	        resultType="com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarInfoDTO">
		select info.id as carInfoId,
		       info.driving_ability as drivingAbility,
		       info.head_city_no AS headCityNo,
		       info.tail_city_no AS tailCityNo,
		       info.car_type as carType,
		       car.signing as signing,
		       car.user_id as userId,
		       car.tyt_cell_phone as phone,
		       driver.verify_status as verifyStatus,
		       info.driver_id as driverId,
		       info.driver_user_id as driverUserId,
		       info.distance_preference as distancePreference,
		       info.driver_tag as driverTag,
		       info.favorable_comment AS favorableComment,
		       info.receiving_orders AS receivingOrders
		from tyt_signing_car_info info
	    left join tyt_invoice_driver driver on info.driver_id = driver.id
	    left join tyt_signing_car car on info.signing_id = car.id
	    left join tyt_signing_car_info_city city on info.id = city.car_info_id
		where car.status = 1 and city.city = "全国"
		limit #{start}, #{pageSize}
	</select>

	<select id="getByCellPhone" resultType="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarDO">
		select * from tyt_signing_car where tyt_cell_phone = #{cellPhone}
	</select>

	<update id="updateCooperNum">
		update tyt_signing_car
		set cooperate_num = cooperate_num + 1,
			update_time   = now()
		where id = #{signingId}
	</update>


	<update id="updateCarStatusByUserId">
		update tyt_signing_car set status = #{status} where user_id = #{userId}
	</update>

	<select id="getSigningCarBlack" resultType="java.lang.Long">
		select DISTINCT info.driver_user_id from tyt_signing_car car
													 left join tyt_signing_car_info info on info.signing_id = car.id
		where car.user_id = #{userId} and info.driver_user_id is not null

	</select>


	<select id="getByUserNoIds" resultType="java.lang.Integer">
		select count(*) from tyt_signing_driver_black where user_id in
		<foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
			#{id}
		</foreach>
		and sign_status = 2
	</select>

	<select id="getSigningId" resultType="com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarUserVO">
		select info.id,black.sign_status as status from tyt_signing_car_info info
				left join tyt_signing_driver_black black on info.driver_user_id = black.user_id
		where info.signing_id = #{id}
	</select>

	<update id="updateNumId">
		update tyt_signing_car_info set assign_num = assign_num + 1,update_time = now() where id = #{id}
	</update>

	<update id="updateSigningNUm">
		update tyt_signing_car_info set assign_num = assign_num + 1,update_time = now() where signing_id = #{id} and phone = #{phone}
	</update>

	<select id="getByUserId" resultType="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarDO">
		select id,status,signing from tyt_signing_car where user_id = #{userId} limit 1
	</select>
</mapper>
