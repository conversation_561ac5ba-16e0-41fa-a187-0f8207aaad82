<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.goodsname.mybatis.mapper.MachineTypeBrandNewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.MachineTypeBrandNewDO">
        <id column="id" property="id" />
        <result column="show_name" property="showName" />
        <result column="top_class" property="topClass" />
        <result column="second_class" property="secondClass" />
        <result column="brand" property="brand" />
        <result column="brand_type" property="brandType" />
        <result column="top_type" property="topType" />
        <result column="second_type" property="secondType" />
        <result column="weight" property="weight" />
        <result column="length" property="length" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="remarks" property="remarks" />
        <result column="general_matches_item" property="generalMatchesItem" />
        <result column="score" property="score" />
        <result column="display" property="display" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, show_name, top_class, second_class, brand, brand_type, top_type, second_type, weight, length, width, height, remarks, general_matches_item, score, display, ctime, mtime
    </sql>

	<select id="getByShowName"
	        resultType="com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.MachineTypeBrandNewDO">
        select *
        from tyt_machine_type_brand_new
        where show_name = #{showName}
        LIMIT 1
    </select>

</mapper>
