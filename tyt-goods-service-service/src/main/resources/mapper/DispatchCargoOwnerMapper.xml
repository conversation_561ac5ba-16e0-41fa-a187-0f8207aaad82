<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.DispatchCargoOwnerMapper">


    <select id="selectSignedByUserId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO">
        select *
        from tyt_dispatch_cargo_owner
        where user_id = #{ownerUserId} and status = 1 and sign_partner = 1 limit 1
    </select>

	<select id="getByUserId"
	        resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO">
		select * from tyt_dispatch_cargo_owner where user_id = #{userId} and status = 1 order by id desc limit 1
	</select>

	<select id="selectByCooperativeId"
	        resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO">
		select *
		from tyt_dispatch_cargo_owner
		where cooperative_id = #{cooperativeId} and status = 1 limit 1
	</select>

	<select id="getByCooperativeId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO">
		select * from tyt_dispatch_cargo_owner where cooperative_id = #{cooperativeId} limit 1
	</select>

	<select id="selectRuleCount" resultType="java.lang.Integer">
		select count(1)
		from tyt_dispatch_cargo_owner
		where user_id = #{ownerUserId}
			and (cooperative_id = (select id from tyt_dispatch_cooperative where cooperative_name = '平台' and status = 1 and delete_flag = 0 limit 1)
		   or cooperative_id = 0 or cooperative_id is null) and status = 1
	</select>
</mapper>
