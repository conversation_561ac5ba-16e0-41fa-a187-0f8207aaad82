<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarAutoDispatchConfigMapper">

    <select id="selectDispatchConfigByRoute"
            resultType="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarAutoDispatchConfigDO">
        select *
        from tyt_special_car_auto_dispatch_config
        where del_status = 0 and status = 1 and start_city = #{startCity} and dest_city = #{destCity}
            limit 1
    </select>
</mapper>
