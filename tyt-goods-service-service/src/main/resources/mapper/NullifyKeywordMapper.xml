<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.goodsname.mybatis.mapper.NullifyKeywordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.NullifyKeywordDO">
        <id column="id" property="id" />
        <result column="keyword_type" property="keywordType" />
        <result column="keyword_value" property="keywordValue" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, keyword_type, keyword_value, ctime, mtime
    </sql>

</mapper>
