<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.order.mybatis.mapper.TransportAfterOrderDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.goods.service.service.biz.order.mybatis.entity.TransportAfterOrderDataDO">
        <id column="id" property="id"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="user_id" property="userId"/>
        <result column="task_content" property="taskContent"/>
        <result column="good_type_name" property="goodTypeName"/>
        <result column="start_city" property="startCity"/>
        <result column="dest_city" property="destCity"/>
        <result column="distance" property="distance"/>
        <result column="weight" property="weight"/>
        <result column="price" property="price"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, user_id, task_content, good_type_name, start_city, dest_city, distance, weight, price,
        create_time, modify_time
    </sql>

    <select id="getExcellentGoodsDealTimeByRoute"
            resultType="com.teyuntong.goods.service.service.biz.order.dto.ExcellentGoodsDealTimeDTO">
        select tm.id as srcMsgId, TIMESTAMPDIFF(MINUTE, tm.ctime, od.create_time) + 1 as dealMinutes
        from tyt_transport_after_order_data od
        left join tyt_transport_main tm on od.src_msg_id = tm.id
        where od.start_city = #{startCity} and od.dest_city = #{destCity} and od.create_time > #{startTime}
              and tm.excellent_goods = 1 and tm.`status` = 4
    </select>

    <!-- 查询最近90天的所有相似货源 -->
    <select id="getSameTransport" resultMap="BaseResultMap">
        select od.src_msg_id, od.user_id, od.good_type_name, od.price, od.create_time
        from tyt_transport_after_order_data od
        left join tyt_transport_main_extend me on od.src_msg_id = me.src_msg_id
        where od.start_city = #{startCity}
        and od.dest_city = #{destCity}
        and od.create_time between #{startDate} and #{endDate}
        and od.distance >= (#{distance} - 10)
        and od.distance &lt;= (#{distance} + 10)
        and od.weight >= #{weightMin}
        and od.weight &lt; #{weightMax}
        and me.use_car_type = 1
        <if test="hasPrice">
            and od.price > 0
        </if>
        order by od.id desc
    </select>

</mapper>
