<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.TransportDispatchCarTypeMapper">

	<select id="selectCarTypeListByWeight" resultType="java.lang.String">
		select car_type
		from tyt_transport_dispatch_car_type
		where status = 1 and start_tonnage &lt;= #{weight} and end_tonnage &gt;= #{weight}
	</select>
</mapper>
