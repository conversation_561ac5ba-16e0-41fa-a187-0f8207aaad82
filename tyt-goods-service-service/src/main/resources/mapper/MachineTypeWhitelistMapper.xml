<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.goodsname.mybatis.mapper.MachineTypeWhitelistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.MachineTypeWhitelistDO">
        <id column="id" property="id" />
        <result column="show_name" property="showName" />
        <result column="top_class" property="topClass" />
        <result column="second_class" property="secondClass" />
        <result column="brand_type" property="brandType" />
        <result column="top_type" property="topType" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, show_name, top_class, second_class, brand_type, top_type, create_name, create_time, modify_name, modify_time
    </sql>

</mapper>
