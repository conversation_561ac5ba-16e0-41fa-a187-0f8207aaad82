<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportBackendMapper">

    <!-- 查询货源运输后台（小程序）表信息 -->
    <select id="selectBySrcMsgId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportBackendDO">
        select *
        from tyt_transport_backend
        where src_msg_id = #{srcMsgId}
        limit 1
    </select>

    <select id="selectBackendSrcMsgIds" resultType="java.lang.Long">
        select src_msg_id
        from tyt_transport_backend
        where src_msg_id IN
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 更新货源（小程序）状态状态 -->
    <update id="updateBySrcMsgId" parameterType="com.teyuntong.goods.service.client.transport.dto.TransportBackendDTO">
        update tyt_transport_backend set mtime =  date_format(now() ,'%Y%m%d%H%i%s')
        <if test="status !=null">
            ,status = #{status}
        </if>
        <if test="carriageTime !=null">
            ,carriage_time=#{carriageTime}
        </if>
        where src_msg_id = #{srcMsgId}
    </update>

    <update id="updateBackendStatusInfo">
        UPDATE tyt_transport_backend SET `status` = #{status} ,order_status =#{orderStatus},cancel_time=NOW()   WHERE src_msg_id =#{srcMsgId}
    </update>

    <update id="updateBackendStatusForDone">
        UPDATE tyt_transport_backend SET `status` = 6 ,order_status =60,carriage_time=NOW()   WHERE src_msg_id =#{srcMsgId}
    </update>
</mapper>
