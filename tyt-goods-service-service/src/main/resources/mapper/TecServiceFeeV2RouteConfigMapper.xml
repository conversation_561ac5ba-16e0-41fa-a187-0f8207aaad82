<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TecServiceFeeV2RouteConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2RouteConfigDO">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="route_type" property="routeType" />
        <result column="start_city" property="startCity" />
        <result column="dest_city" property="destCity" />
        <result column="batch_no" property="batchNo" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="modify_time" property="modifyTime" />
        <result column="modify_user_id" property="modifyUserId" />
        <result column="modify_user_name" property="modifyUserName" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, route_type, start_city, dest_city, batch_no, create_time, create_user_id, create_user_name, modify_time, modify_user_id, modify_user_name, del_status
    </sql>

	<select id="getRouteConfigs"
	        resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2RouteConfigDO">
        select *
        from tyt_tec_service_fee_v2_route_config
        where del_status = 0
        and config_id = #{configId}
        and (
            route_type = 0 or (
                start_city = #{startCity} and dest_city = #{destCity}
            )
        )
    </select>

</mapper>
