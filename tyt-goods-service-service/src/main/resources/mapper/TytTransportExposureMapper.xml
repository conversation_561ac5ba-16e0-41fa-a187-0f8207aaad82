<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TytTransportExposureMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TytTransportExposureDO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="has_call" jdbcType="INTEGER" property="hasCall" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="change_id" jdbcType="BIGINT" property="changeId" />
  </resultMap>

  <select id="getMaxChangeId" resultType="java.lang.Long">
    select max(change_id) from tyt_transport_exposure
  </select>

  <update id="updateStatusAndChangeIdBuSrcMsgId">
    update tyt_transport_exposure SET status = #{status} AND change_id = #{changeId} where src_msg_id = #{srcMsgId}
  </update>
</mapper>
