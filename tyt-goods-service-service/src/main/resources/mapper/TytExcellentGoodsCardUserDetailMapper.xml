<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.mapper.TytExcellentGoodsCardUserDetailMapper">
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.entity.TytExcellentGoodsCardUserDetail">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="config_id" jdbcType="BIGINT" property="configId"/>
        <result column="import_id" jdbcType="BIGINT" property="importId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="first_refresh_times" jdbcType="INTEGER" property="firstRefreshTimes"/>
        <result column="first_refresh_interval" jdbcType="INTEGER" property="firstRefreshInterval"/>
        <result column="second_refresh_times" jdbcType="INTEGER" property="secondRefreshTimes"/>
        <result column="second_refresh_interval" jdbcType="INTEGER" property="secondRefreshInterval"/>
        <result column="valid_date_begin" jdbcType="TIMESTAMP" property="validDateBegin"/>
        <result column="valid_date_end" jdbcType="TIMESTAMP" property="validDateEnd"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <sql id="base_sql">
        id
        , config_id, import_id, user_id, type, title, content, first_refresh_times, first_refresh_interval
         , second_refresh_times, second_refresh_interval, valid_date_begin, valid_date_end, create_time, create_user_name
         , create_user_id, update_time, update_user_name, update_user_id
    </sql>

    <select id="getAllNoUseCarListByUserId" resultMap="BaseResultMap">
        select
        <include refid="base_sql"/>
        from tyt_excellent_goods_card_user_detail
        where user_id = #{userId} and type in (1, 2) and valid_date_end &gt; now() and use_type=1
        order by create_time desc limit #{startNum}, #{pageSize}
    </select>

    <select id="getAllCanUseCarCountNumMax100AndLimitTimeByUserId" resultType="com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailCanUseCountVO">
        select count(1) as canUseCarNum, min(valid_date_end) as earliestExpirationTime
        from (select <include refid="base_sql"/> from tyt_excellent_goods_card_user_detail
        where user_id = #{userId}
          and type = 2
          and valid_date_begin &lt; now()
          and valid_date_end &gt; now()
          and use_type=1
        order by valid_date_end limit 100) tab
    </select>

    <select id="getAllCanUseCarListByUserIdPage" resultMap="BaseResultMap">
        select
        <include refid="base_sql"/>
        from tyt_excellent_goods_card_user_detail
        where user_id = #{userId}
        and type = 2
        and valid_date_begin &lt; now()
        and valid_date_end &gt; now()
        and use_type=1
        order by valid_date_end, first_refresh_times desc, second_refresh_times desc limit #{startNum}, #{pageSize}
    </select>

    <select id="getAllCanUseCarListByUserId" resultMap="BaseResultMap">
        select
        <include refid="base_sql"/>
        from tyt_excellent_goods_card_user_detail
        where user_id = #{userId}
         and type = 2
         and valid_date_begin &lt; now()
         and valid_date_end &gt; now()
         and use_type=1
        order by valid_date_end, first_refresh_times desc, second_refresh_times desc
    </select>

</mapper>