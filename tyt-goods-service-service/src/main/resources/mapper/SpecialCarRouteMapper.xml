<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarRouteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarRouteDO">
        <id column="id" property="id" />
        <result column="special_id" property="specialId" />
        <result column="start_city" property="startCity" />
        <result column="dest_city" property="destCity" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, special_id, start_city, dest_city, create_time, modify_time
    </sql>


    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tyt_special_car_route
        <include refid="Base_Column_List"/>
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.specialId},
            #{item.startCity},
            #{item.destCity},
            #{item.createTime},
            #{item.modifyTime}
            )
        </foreach>
    </insert>
</mapper>
