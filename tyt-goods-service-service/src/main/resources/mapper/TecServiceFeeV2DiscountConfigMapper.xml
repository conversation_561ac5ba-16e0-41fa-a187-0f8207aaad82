<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TecServiceFeeV2DiscountConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2DiscountConfigDO">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="route_id" property="routeId" />
        <result column="stage_id" property="stageId" />
        <result column="proportion_id" property="proportionId" />
        <result column="discount_time" property="discountTime" />
        <result column="discount" property="discount" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="modify_time" property="modifyTime" />
        <result column="modify_user_id" property="modifyUserId" />
        <result column="modify_user_name" property="modifyUserName" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, route_id, stage_id, proportion_id, discount_time, discount, create_time, create_user_id, create_user_name, modify_time, modify_user_id, modify_user_name, del_status
    </sql>

	<select id="getByProportionIdAndDiscountTimeMin"
	        resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2DiscountConfigDO">
        select *
        from tyt_tec_service_fee_v2_discount_config
        where del_status = 0
          and proportion_id = #{proportionId}
          and discount_time >= #{discountTime}
        order by discount_time
        limit 1
    </select>

    <select id="getByProportionId"
            resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeV2DiscountConfigDO">
        select *
        from tyt_tec_service_fee_v2_discount_config
        where del_status = 0
          and proportion_id = #{proportionId}
        order by discount_time
    </select>

</mapper>
