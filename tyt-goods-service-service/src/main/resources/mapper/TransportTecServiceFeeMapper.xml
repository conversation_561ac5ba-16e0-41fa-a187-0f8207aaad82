<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TransportTecServiceFeeMapper">

    <select id="getBySrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TransportTecServiceFeeDO">
        SELECT * FROM tyt_transport_tec_service_fee WHERE src_msg_id = #{srcMsgId}
    </select>

    <select id="isCommissionTransport" resultType="integer">
        SELECT count(1) FROM tyt_transport_tec_service_fee WHERE src_msg_id = #{srcMsgId}
    </select>

    <delete id="deleteBySrcMsgId">
        DELETE FROM tyt_transport_tec_service_fee WHERE src_msg_id = #{srcMsgId}
    </delete>

</mapper>