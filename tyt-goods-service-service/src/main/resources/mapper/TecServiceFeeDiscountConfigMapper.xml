<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TecServiceFeeDiscountConfigMapper">

    <select id="getByProportionId"
            resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeDiscountConfigDO">
        select * from tyt_tec_service_fee_discount_config
        where proportion_id = #{proportionId}
        order by discount_time
    </select>

    <select id="getByProportionIdAndDiscountTimeMin"
            resultType="com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TecServiceFeeDiscountConfigDO">
        select * from tyt_tec_service_fee_discount_config
        where proportion_id = #{proportionId}
        and discount_time >= #{discountTime}
        order by discount_time
        limit 1
    </select>
</mapper>
