<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.AppCallLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.AppCallLogDO">
        <id column="id" property="id"/>
        <result column="call_module" property="callModule"/>
        <result column="from_car" property="fromCar"/>
        <result column="car_id" property="carId"/>
        <result column="call_time" property="callTime"/>
        <result column="caller_id" property="callerId"/>
        <result column="called_info_id" property="calledInfoId"/>
        <result column="call_result_code" property="callResultCode"/>
        <result column="call_result_name" property="callResultName"/>
        <result column="marker_owner_codes" property="markerOwnerCodes"/>
        <result column="marker_owner_names" property="markerOwnerNames"/>
        <result column="marker_owner_code" property="markerOwnerCode"/>
        <result column="marker_owner_name" property="markerOwnerName"/>
        <result column="pub_user_id" property="pubUserId"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="reference" property="reference"/>
        <result column="plat_id" property="platId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, call_module, from_car, car_id, call_time, caller_id, called_info_id, call_result_code, call_result_name,
        marker_owner_codes, marker_owner_names, marker_owner_code, marker_owner_name, pub_user_id, src_msg_id,
        reference, plat_id, create_time
    </sql>

    <!-- 查询一段时间内的拨打次数 -->
    <select id="getCallCountOfPeriod" resultType="integer">
        select count(*) from tyt_app_call_log
        where caller_id = #{userId}
        <if test="startTime != null and endTime != null">
            and call_time between #{startTime} and #{endTime}
        </if>
    </select>

    <!-- 查询某货源的拨打次数 -->
    <select id="getCallCountOfGoods" resultType="integer">
        SELECT count(*)
        FROM tyt_app_call_log
        WHERE src_msg_id = #{srcMsgId}
        <if test="userId != null">
            AND caller_id = #{userId}
        </if>
    </select>

    <select id="getLastCallTimeOffset" resultType="date">
        SELECT create_time
        FROM tyt_app_call_log
        WHERE src_msg_id = #{srcMsgId}
        AND create_time >= CURDATE()
        ORDER BY create_time
        LIMIT 1 OFFSET #{offset}
    </select>

    <select id="getViewLogCountBySrcMsgId" resultType="java.lang.Integer">
        select count(1) from tyt_transport_view_log where ts_id = #{srcMsgId}
    </select>
</mapper>
