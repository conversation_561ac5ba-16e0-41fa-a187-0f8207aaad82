<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportEnterpriseLogMapper">
    
    <select id="getBySrcMsgIds"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportEnterpriseLogDO">
        select *
        from tyt_transport_enterprise_log
        where src_msg_id in ( <foreach collection="srcMsgIds" item="i" separator=",">#{i}</foreach> )
    </select>


    <select id="getBySrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportEnterpriseLogDO">
        select *
        from tyt_transport_enterprise_log
        where src_msg_id = #{srcMsgId}
    </select>

    <delete id="deleteBySrcMsgId">
        delete
        from tyt_transport_enterprise_log
        where src_msg_id = #{srcMsgId}
    </delete>

    <select id="getXHLConsigneeData"
            resultType="com.teyuntong.goods.service.client.transport.vo.InvoiceXHLConsigneeDataVO">
        select consignee_name, consignee_tel, consignee_enterprise_name, max(modify_time)
        from tyt_transport_enterprise_log
        where certigier_user_id = #{userId}
          and consignee_name is not null
          and consignee_name != ''
          and consignee_tel is not null
          and consignee_tel != ''
          and consignee_enterprise_name is not null
          and consignee_enterprise_name != ''
        group by consignee_name, consignee_tel, consignee_enterprise_name
        order by max(modify_time) desc
        limit 100
    </select>
</mapper>
