<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TytTransportSyncYmmMapper">


    <select id="getByCargoId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportSyncYmmDO">
        SELECT * FROM tyt_transport_sync_ymm WHERE cargo_id = #{cargoId}
    </select>

    <select id="selectTytTransportSyncYmmBySrcId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportSyncYmmDO">
        select * from tyt_transport_sync_ymm where is_delete = 0 and src_msg_id = #{srcMsgId} and transport_status = 0 and sync_status = 0 order by id desc limit 1
    </select>

</mapper>