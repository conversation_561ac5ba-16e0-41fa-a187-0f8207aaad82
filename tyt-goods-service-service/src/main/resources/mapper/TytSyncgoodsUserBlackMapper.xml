<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytSyncgoodsUserBlackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytSyncgoodsUserBlackDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="create_username" property="createUsername" />
        <result column="create_user_id" property="createUserId" />
        <result column="update_time" property="updateTime" />
        <result column="update_username" property="updateUsername" />
        <result column="update_user_id" property="updateUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, reason, create_time, create_username, create_user_id, update_time, update_username, update_user_id
    </sql>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tyt_syncgoods_user_black
        where user_id = #{userId} limit 1
    </select>

    <insert id="insertBlackRecord">
        insert into tyt_syncgoods_user_black (
            user_id, reason, create_time, create_username, update_time, update_username
        ) values (
            #{userId},
            #{reason},
            now(),
            #{createUsername},
            now(),
            #{createUsername}
        )
    </insert>
</mapper>
