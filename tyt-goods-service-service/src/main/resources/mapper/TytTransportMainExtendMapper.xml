<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TytTransportMainExtendMapper">

    <select id="getTransportIsSeckillGoods" resultType="integer">
        select count(1)
        from tyt_transport_main_extend where src_msg_id = #{srcMsgId} and seckill_goods = 1
    </select>

    <select id="getSeckillGoodsHaveSuccess" resultType="integer">
        select count(1)
        from tyt_transport_order_snapshot where src_msg_id = #{srcMsgId} and order_allocate_state = 1
    </select>

    <select id="getSeckillGoodsHaveError" resultType="integer">
        select count(1)
        from tyt_transport_order_snapshot where src_msg_id = #{srcMsgId} and order_allocate_state = 3
    </select>

</mapper>