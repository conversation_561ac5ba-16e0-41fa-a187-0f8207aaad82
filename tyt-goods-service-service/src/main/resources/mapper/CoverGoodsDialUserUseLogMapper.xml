<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.cover.mybatis.mapper.CoverGoodsDialUserUseLogMapper">

	<select id="countByUserIdAndChangeType" resultType="java.lang.Integer">
		select count(*)
		from tyt_cover_goods_dial_user_use_log
		where user_id = #{userId}
		  and change_type = #{changeType}
	</select>
</mapper>
