<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMbMergeMapper">


    <select id="selectBySrcMsgId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMbMergeDO">
        select *
        from tyt_transport_mb_merge
        where src_msg_id = #{srcMsgId}
        order by create_time desc limit 1
    </select>

    <select id="selectBeforeVersionJson"
            resultType="com.teyuntong.goods.service.service.biz.transport.dto.TransportYmmListDTO">
        select
            ttmm.cargo_id  cargoId,
            ttmm.src_msg_id  srcMsgId,
            ttmm.cargo_version cargoVersion,
            tmcs.info_json infoJson
        from tyt_transport_mb_merge ttmm
                 left join tyt_mb_cargo_sync_log tmcs on tmcs.cargo_id = ttmm.cargo_id
        where ttmm.src_msg_id=#{srcMsgId}
          and ttmm.cargo_version = tmcs.cargo_version order by ttmm.create_time desc limit 1
    </select>

    <select id="selectByCargoId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMbMergeDO">
        select src_msg_id
        from tyt_transport_mb_merge
        where cargo_id = #{cargoId}
          and status = 0
        limit 1
    </select>

    <update id="updateCargoMerge">
        update tyt_transport_mb_merge set status = 1 where src_msg_id = #{srcMsgId}
    </update>
</mapper>
