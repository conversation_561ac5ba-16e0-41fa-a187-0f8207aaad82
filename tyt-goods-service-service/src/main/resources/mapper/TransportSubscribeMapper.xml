<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportSubscribeMapper">

    <select id="matchSubscribeUser"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportSubscribeDO">
        select user_id, max(order_id) order_id
        from tyt_transport_subscribe
        where `status` = 1
          and #{loadingDate} >= loading_date
          and date_add(loading_date, INTERVAL 2 DAY) >= #{loadingDate}
          and #{weight} >= weight_min
          and weight_max >= #{weight}
          and start_coord_x between #{startCoordX} - #{startRange} and #{startCoordX} + #{startRange}
          and start_coord_y between #{startCoordY} - #{startRange} and #{startCoordY} + #{startRange}
          and dest_coord_x between #{destCoordX} - #{destRange} and #{destCoordX} + #{destRange}
          and dest_coord_y between #{destCoordY} - #{destRange} and #{destCoordY} + #{destRange}
        group by user_id
    </select>
</mapper>
