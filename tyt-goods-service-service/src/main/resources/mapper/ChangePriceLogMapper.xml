<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.ChangePriceLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ChangePriceLogDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="price" property="price" />
        <result column="publish_type" property="publishType" />
        <result column="operation_type" property="operationType" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, price, publish_type, operation_type, create_time
    </sql>

    <!-- 根据货源ID查询最近的一条价格变动记录 -->
    <select id="getLatestLogBySrcMsgId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tyt_change_price_log
        WHERE src_msg_id = #{srcMsgId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper>
