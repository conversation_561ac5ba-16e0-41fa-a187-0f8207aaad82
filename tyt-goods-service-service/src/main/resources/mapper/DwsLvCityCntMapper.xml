<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.archive.mapper.DwsLvCityCntMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.archive.entity.DwsLvCityCnt">
        <id column="user_id" property="userId" />
        <id column="city" property="city" />
        <result column="cnt" property="cnt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, city, cnt
    </sql>

    <select id="queryCarUserCityList" resultMap="BaseResultMap">
        select *
        from dws_lv_city_cnt
        where user_id = #{carUserId} and city in
        <foreach collection="cityList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryCarUserTopCities" resultMap="BaseResultMap">
        select *
        from dws_lv_city_cnt
        where user_id = #{carUserId}
        <if test="cityList != null and cityList.size() > 0">
            and city not in
            <foreach collection="cityList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by cnt desc limit #{limitNum}
    </select>

</mapper>
