<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.CustomInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.CustomInformationDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="goods_phone" property="goodsPhone"/>
        <result column="name" property="name"/>
        <result column="source" property="source"/>
        <result column="indentity_type" property="indentityType"/>
        <result column="publish_good_type" property="publishGoodType"/>
        <result column="custom_levels" property="customLevels"/>
        <result column="market_maintenance_personnel" property="marketMaintenancePersonnel"/>
        <result column="market_maintenance_personnel_user_id" property="marketMaintenancePersonnelUserId"/>
        <result column="dispatch_maintenance_personnel" property="dispatchMaintenancePersonnel"/>
        <result column="dispatch_maintenance_personnel_user_id" property="dispatchMaintenancePersonnelUserId"/>
        <result column="dispatch_intention_levels" property="dispatchIntentionLevels"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="goods_city" property="goodsCity"/>
        <result column="company_name" property="companyName"/>
        <result column="company_voucher" property="companyVoucher"/>
        <result column="position" property="position"/>
        <result column="mobile_belonging_place" property="mobileBelongingPlace"/>
        <result column="status" property="status"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="performance_time" property="performanceTime"/>
        <result column="market_allot_time" property="marketAllotTime"/>
        <result column="dispatch_allot_time" property="dispatchAllotTime"/>
        <result column="sys_data_init" property="sysDataInit"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, goods_phone, name, source, indentity_type, publish_good_type, custom_levels, market_maintenance_personnel, market_maintenance_personnel_user_id, dispatch_maintenance_personnel, dispatch_maintenance_personnel_user_id, dispatch_intention_levels, detail_address, goods_city, company_name, company_voucher, position, mobile_belonging_place, status, create_user_name, create_user_id, update_user_name, create_time, update_user_id, update_time, performance_time, market_allot_time, dispatch_allot_time, sys_data_init
    </sql>

    <!-- 查询客户信息 -->
    <select id="selectOne"
            parameterType="com.teyuntong.goods.service.client.transport.dto.CustomInformationDTO"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.CustomInformationDO">
        select
        <include refid="Base_Column_List"/>
        from tyt_custom_information
        where goods_phone = #{goodsPhone} and status = 1
        limit 1
    </select>



</mapper>
