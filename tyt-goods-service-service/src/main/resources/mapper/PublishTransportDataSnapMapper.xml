<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.PublishTransportDataSnapMapper">

    <!-- 查询用户出发地历史记录 -->
    <select id="selectStartLocationHistory" resultType="com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO">
        SELECT 
            start_provinc as provinc, 
            start_city as city, 
            start_area as area, 
            start_latitude as latitude, 
            start_longitude as longitude, 
            start_coord_x as coordX, 
            start_coord_y as coordY, 
            start_detail_add as detailAdd, 
            start_point as point
        FROM tyt_publish_transport_data_snap
        WHERE user_id = #{userId} 
          AND create_time > #{startTime}
        GROUP BY start_coord_x, start_coord_y
        ORDER BY MAX(create_time) DESC limit 20
    </select>

    <!-- 查询用户目的地历史记录 -->
    <select id="selectDestLocationHistory" resultType="com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO">
        SELECT 
            dest_provinc as provinc, 
            dest_city as city, 
            dest_area as area, 
            dest_latitude as latitude, 
            dest_longitude as longitude, 
            dest_coord_x as coordX, 
            dest_coord_y as coordY, 
            dest_detail_add as detailAdd, 
            dest_point as point
        FROM tyt_publish_transport_data_snap
        WHERE user_id = #{userId} 
          AND create_time > #{startTime}
        GROUP BY dest_coord_x, dest_coord_y
        ORDER BY MAX(create_time) DESC limit 20
    </select>

    <!-- 查询用户货物历史记录 -->
    <select id="selectGoodsHistory" resultType="com.teyuntong.goods.service.client.transport.vo.PublishTaskContentHistoryVO">
        SELECT 
            task_content as taskContent, 
            match_item_id as matchItemId, 
            type,
            brand, 
            good_type_name as goodTypeName, 
            weight, 
            length, 
            wide, 
            high
        FROM tyt_publish_transport_data_snap
        WHERE user_id = #{userId} 
          AND create_time > #{startTime}
        GROUP BY task_content
        ORDER BY MAX(create_time) DESC limit 20
    </select>

</mapper>
