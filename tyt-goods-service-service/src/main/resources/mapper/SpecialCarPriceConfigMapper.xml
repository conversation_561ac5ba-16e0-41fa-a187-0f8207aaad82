<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.SpecialCarPriceConfigMapper">


    <select id="selectMatchPriceConfig"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.SpecialCarPriceConfigDO">
        select *
        from tyt_special_car_price_config
        where del_status = 0
          and start_city = #{startCity}
          and dest_city = #{destCity}
          and cargo_owner_id = #{cargoOwnerId}
          and start_tonnage &lt; #{weight}
          and end_tonnage >= #{weight}
          and status = 1
        limit 1
    </select>
    <select id="countByOwnerAndRoute" resultType="java.lang.Integer">
        select count(*)
        from tyt_special_car_price_config
        where del_status = 0
          and status = 1
          and start_city = #{startCity}
          and dest_city = #{destCity}
          and cargo_owner_id = #{cargoOwnerId}
    </select>

    <select id="countMatchPriceConfigCityAndRule" resultType="integer">
        select count(1)
        from tyt_special_car_price_config
        where del_status = 0
          and start_city = #{startCity}
          and dest_city = #{destCity}
          and cargo_owner_id != (select id
      from tyt_dispatch_cooperative
      where cooperative_name = '平台' and status = 1 and delete_flag = 0 limit 1)
          and status = 1
    </select>

</mapper>
