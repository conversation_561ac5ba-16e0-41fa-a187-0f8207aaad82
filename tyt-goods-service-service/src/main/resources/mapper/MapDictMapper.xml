<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.mapdic.mybatis.mapper.MapDictMapper">

    <select id="getDistanceCity" resultType="java.lang.Integer">
        select distance
        from tyt_map_dict
        where start_city = #{city} and start_area = #{district} and dest_city = #{startCity} and dest_area = #{startArea}
    </select>
</mapper>
