<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportViewLogMapper">

    <insert id="insertIgnore">
        insert ignore into tyt_transport_view_log (user_id,ts_id,client_version,client_sign) values
            (#{userId}, #{tsId}, #{clientVersion}, #{clientSign})
    </insert>

    <select id="getLastViewTimeOffset" resultType="date">
        SELECT ctime
        FROM tyt_transport_view_log
        WHERE ts_id = #{srcMsgId}
        and ctime >= CURDATE()
        ORDER BY ctime
        LIMIT 1 OFFSET #{offset}
    </select>

    <select id="isViewed" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_view_log
        where user_id = #{userId} and ts_id = #{srcMsgId}
    </select>

    <select id="getViewNumOfTsId" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_view_log
        where ts_id = #{srcMsgId}
    </select>

</mapper>
