package com.teyuntong.goods.service.service.rpc.publish;

import com.teyuntong.goods.service.client.publish.dto.DealMinutesDTO;
import com.teyuntong.goods.service.client.publish.dto.SpecialCarInfoDTO;
import com.teyuntong.goods.service.client.publish.vo.CooperativeVO;
import com.teyuntong.goods.service.client.publish.vo.DealMinutesVO;
import com.teyuntong.goods.service.client.publish.vo.SpecialCarInfoVO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.SpecialCarPriceConfigDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCargoOwnerService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCompanyService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCooperativeService;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.enums.UserTypeEnum;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.builder.CalcSpecialGoodsPriceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.SPECIAL_CAR_DISPATCH_TIME_NORMAL;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.SPECIAL_CAR_DISPATCH_TIME_NOT_NORMAL;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 *
 *
 * <AUTHOR>
 * @since 2025-07-07 10:37
 */
class TransportPublishRpcServiceImplTest {
    @InjectMocks
    private TransportPublishRpcServiceImpl transportPublishRpcService;

    @Mock
    private TytConfigRemoteService tytConfigRemoteService;

    @Mock
    private CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;

    @Mock
    private TransportAfterOrderDataService transportAfterOrderDataService;

    @Mock
    private DispatchCompanyService dispatchCompanyService;

    @Mock
    private DispatchCargoOwnerService dispatchCargoOwnerService;

    @Mock
    private DispatchCooperativeService dispatchCooperativeService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // 测试优车货源的预计成交时长
    @Test
    public void testGetDealMinutes_ExcellentGoods() {
        DealMinutesDTO dto = new DealMinutesDTO();
        dto.setPublishGoodsType(PublishGoodsTypeEnum.EXCELLENT_GOODS.getCode());
        dto.setStartCity("保定市");
        dto.setDestCity("北京市");

        when(transportAfterOrderDataService.getExcellentPricePopupPrompt(any(TransportMainDO.class))).thenReturn(30);

        DealMinutesVO result = transportPublishRpcService.getDealMinutes(dto);

        assertNotNull(result);
        assertEquals(Integer.valueOf(40), result.getDealMinutes());  // 30 + 10
        assertNull(result.getShowDriverDriving());
        assertNull(result.getHideSpecialCarRule());
    }

    // 测试专车货源的预计成交时长
    @Test
    public void testGetDealMinutes_SpecialGoods_NormalTime() {
        DealMinutesDTO dto = new DealMinutesDTO();
        dto.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());
        dto.setStartCity("杭州");
        dto.setDestCity("宁波");
        dto.setGoodTypeName("挖掘机");
        dto.setUserId(1001L);

        when(tytConfigRemoteService.getIntValue(eq(SPECIAL_CAR_DISPATCH_TIME_NORMAL), anyInt())).thenReturn(10);
        when(calcSpecialGoodsPriceService.countMatchPriceConfigCityAndRule(anyString(), anyString())).thenReturn(0);
        SpecialCarPriceConfigDO priceConfigDO = new SpecialCarPriceConfigDO();
        priceConfigDO.setDrivingFee(new BigDecimal(100));
        when(calcSpecialGoodsPriceService.selectMatchPriceConfig(anyLong(), anyLong(), anyString(), anyString(), anyString())).thenReturn(priceConfigDO);
        when(dispatchCompanyService.countByUserId(anyLong())).thenReturn(0);
        when(dispatchCargoOwnerService.selectByUserId(anyLong())).thenReturn(null);

        DealMinutesVO result = transportPublishRpcService.getDealMinutes(dto);

        assertNotNull(result);
        assertEquals(Integer.valueOf(10), result.getDealMinutes());
        assertNull(result.getShowDriverDriving());
        assertEquals(1, result.getHideSpecialCarRule().intValue());
    }

    // 测试专车货源的非正常派单时间
    @Test
    public void testGetDealMinutes_SpecialGoods_NotNormalTime() {
        DealMinutesDTO dto = new DealMinutesDTO();
        dto.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());
        dto.setStartCity("广州");
        dto.setDestCity("深圳");
        dto.setGoodTypeName("挖掘机");
        dto.setUserId(1002L);

        when(tytConfigRemoteService.getIntValue(eq(SPECIAL_CAR_DISPATCH_TIME_NORMAL), anyInt())).thenReturn(10);
        when(tytConfigRemoteService.getIntValue(eq(SPECIAL_CAR_DISPATCH_TIME_NOT_NORMAL), anyInt())).thenReturn(20);
        when(calcSpecialGoodsPriceService.countMatchPriceConfigCityAndRule(anyString(), anyString())).thenReturn(5);

        when(dispatchCompanyService.countByUserId(anyLong())).thenReturn(0);
        when(dispatchCargoOwnerService.selectByUserId(anyLong())).thenReturn(mock(DispatchCargoOwnerDO.class));
        when(dispatchCargoOwnerService.selectRuleCount(anyLong())).thenReturn(1);

        DealMinutesVO result = transportPublishRpcService.getDealMinutes(dto);

        assertNotNull(result);
        assertEquals(Integer.valueOf(20), result.getDealMinutes());
        assertNull(result.getShowDriverDriving());
        assertEquals(1, result.getHideSpecialCarRule().intValue());
    }

    // 测试司机驾驶能力展示逻辑
    @Test
    public void testGetDealMinutes_ShowDriverDriving() {
        DealMinutesDTO dto = new DealMinutesDTO();
        dto.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());
        dto.setStartCity("成都");
        dto.setDestCity("重庆");
        dto.setGoodTypeName("挖掘机");
        dto.setUserId(1003L);
        dto.setCargoOwnerId(2L);
        dto.setWeight("20");

        when(tytConfigRemoteService.getIntValue(eq(SPECIAL_CAR_DISPATCH_TIME_NORMAL), anyInt())).thenReturn(10);
        when(calcSpecialGoodsPriceService.countMatchPriceConfigCityAndRule(anyString(), anyString())).thenReturn(0);

        SpecialCarPriceConfigDO mockConfig = new SpecialCarPriceConfigDO();
        mockConfig.setDrivingFee(new BigDecimal("100"));
        when(calcSpecialGoodsPriceService.selectMatchPriceConfig(anyLong(), anyLong(), anyString(), anyString(), anyString())).thenReturn(mockConfig);

        when(dispatchCompanyService.countByUserId(anyLong())).thenReturn(0);
        when(dispatchCargoOwnerService.selectByUserId(anyLong())).thenReturn(null);

        DealMinutesVO result = transportPublishRpcService.getDealMinutes(dto);

        assertNotNull(result);
        assertEquals(Integer.valueOf(10), result.getDealMinutes());
        assertEquals(1, result.getShowDriverDriving().intValue());
        assertEquals(1, result.getHideSpecialCarRule().intValue());
    }

    // 测试当用户ID为空时的默认返回值
    @Test
    public void testGetSpecialCarInfo_UserIdIsNull() {
        SpecialCarInfoDTO dto = new SpecialCarInfoDTO();
        dto.setUserId(null);

        SpecialCarInfoVO result = transportPublishRpcService.getSpecialCarInfo(dto);

        assertNotNull(result);
        assertNull(result.getUserType());
        assertNull(result.getCooperativeList());
    }

    // 测试用户是代调账号的情况
    @Test
    public void testGetSpecialCarInfo_UserIsDispatchUser() {
        SpecialCarInfoDTO dto = new SpecialCarInfoDTO();
        dto.setUserId(1001L);
        dto.setCooperativeName("宏信建发");

        when(dispatchCompanyService.countByUserId(1001L)).thenReturn(1);  // 是代调账号
        when(dispatchCooperativeService.selectListByName("宏信建发")).thenReturn(Arrays.asList(new CooperativeVO(), new CooperativeVO()));

        SpecialCarInfoVO result = transportPublishRpcService.getSpecialCarInfo(dto);

        assertNotNull(result);
        assertEquals(UserTypeEnum.DISPATCH_USER.getCode(), result.getUserType());
        assertNotNull(result.getCooperativeList());
        assertEquals(2, result.getCooperativeList().size());
    }

    // 测试用户是非代调账号，且不是专车货主
    @Test
    public void testGetSpecialCarInfo_NotDispatchUserNotSpecialCargoOwner() {
        SpecialCarInfoDTO dto = new SpecialCarInfoDTO();
        dto.setUserId(1002L);

        when(dispatchCompanyService.countByUserId(1002L)).thenReturn(0);  // 非代调账号
        when(dispatchCargoOwnerService.selectSignedByUserId(1002L)).thenReturn(null);  // 不是专车货主

        SpecialCarInfoVO result = transportPublishRpcService.getSpecialCarInfo(dto);

        assertNotNull(result);
        assertEquals(UserTypeEnum.PLAT_USER.getCode(), result.getUserType());
        assertNull(result.getCooperativeList());
    }

    // 测试用户是非代调账号，但属于签约用户（非平台）
    @Test
    public void testGetSpecialCarInfo_SignedUser() {
        SpecialCarInfoDTO dto = new SpecialCarInfoDTO();
        dto.setUserId(1003L);

        when(dispatchCompanyService.countByUserId(1003L)).thenReturn(0);  // 非代调账号

        DispatchCargoOwnerDO owner = new DispatchCargoOwnerDO();
        owner.setCooperativeId(999L);  // 假设签约合作商ID不等于platId
        when(dispatchCargoOwnerService.selectSignedByUserId(1003L)).thenReturn(owner);

        when(dispatchCooperativeService.selectPlatId()).thenReturn(100L);  // 平台ID

        SpecialCarInfoVO result = transportPublishRpcService.getSpecialCarInfo(dto);

        assertNotNull(result);
        assertEquals(UserTypeEnum.SIGNED_USER.getCode(), result.getUserType());
        assertNull(result.getCooperativeList());
    }

    // 测试用户是非代调账号，是专车货主但签约合作商为平台或空
    @Test
    public void testGetSpecialCarInfo_PlatformCargoOwner() {
        SpecialCarInfoDTO dto = new SpecialCarInfoDTO();
        dto.setUserId(1004L);

        when(dispatchCompanyService.countByUserId(1004L)).thenReturn(0);  // 非代调账号

        DispatchCargoOwnerDO owner = new DispatchCargoOwnerDO();
        owner.setCooperativeId(100L);  // 签约合作商为平台
        when(dispatchCargoOwnerService.selectSignedByUserId(1004L)).thenReturn(owner);

        when(dispatchCooperativeService.selectPlatId()).thenReturn(100L);  // 平台ID

        SpecialCarInfoVO result = transportPublishRpcService.getSpecialCarInfo(dto);

        assertNotNull(result);
        assertEquals(UserTypeEnum.PLAT_USER.getCode(), result.getUserType());
        assertNull(result.getCooperativeList());
    }
}