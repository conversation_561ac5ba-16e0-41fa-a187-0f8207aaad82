package com.teyuntong.goods.service.service.rpc.publish;

import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.client.publish.dto.CalculatePriceDTO;
import com.teyuntong.goods.service.client.publish.dto.PublishGoodsTypeDTO;
import com.teyuntong.goods.service.client.publish.vo.PublishGoodsTypeResultVO;
import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.client.transport.vo.TransportMainExtendVO;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCargoOwnerService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCooperativeService;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.remote.user.UserLimitRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.builder.CalcSpecialGoodsPriceService;
import com.teyuntong.user.service.client.limit.vo.DepositBlockCheckResultRpcVO;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @since 2025/07/02 20:13
 */
class TransportPublishTypeRpcServiceImplTest {
    @Mock
    CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;
    @Mock
    TransportMainRpcService transportMainRpcService;
    @Mock
    UserPermissionRemoteService userPermissionRemoteService;
    @Mock
    DispatchCargoOwnerService dispatchCargoOwnerService;
    @Mock
    DispatchCooperativeService dispatchCooperativeService;
    @Mock
    ThPriceService thPriceRpcService;
    @Mock
    UserLimitRemoteService userLimitRemoteService;

    @InjectMocks
    TransportPublishTypeRpcServiceImpl transportPublishTypeRpcServiceImpl;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        // 使用反射设置私有的threadPoolExecutor字段
        java.lang.reflect.Field field = TransportPublishTypeRpcServiceImpl.class.getDeclaredField("threadPoolExecutor");
        field.setAccessible(true);
        field.set(transportPublishTypeRpcServiceImpl, Executors.newFixedThreadPool(4));
    }

    @Test
    void testGetPublishGoodsType() {
        // 创建CalcSpecialGoodsPriceResultDTO并设置必要字段
        CalcSpecialGoodsPriceResultDTO calcResult = new CalcSpecialGoodsPriceResultDTO();
        calcResult.setPrice(new BigDecimal("1000"));
        calcResult.setLowerLimitPrice(new BigDecimal("800"));
        calcResult.setPerkPrice(new BigDecimal("50"));

        // 创建CarryPriceVO并设置必要字段，避免空指针异常
        CarryPriceVO carryPriceVO = new CarryPriceVO();
        carryPriceVO.setThMinPrice(1000);
        carryPriceVO.setThMaxPrice(2000);
        carryPriceVO.setFixPriceMin(1200);
        carryPriceVO.setFixPriceMax(1800);
        carryPriceVO.setFixPriceFast(1500);

        // 创建TransportMainVO并设置必要字段
        TransportMainVO transportMainVO = new TransportMainVO();
        transportMainVO.setPublishGoodsType(1);
        transportMainVO.setPrice("1500");

        // 创建TransportMainExtendVO并设置必要字段
        TransportMainExtendVO transportMainExtendVO = new TransportMainExtendVO();
        transportMainExtendVO.setUseCarType(1);

        // 创建PublishGoodsTypeResultVO
        PublishGoodsTypeResultVO carpoolResult = new PublishGoodsTypeResultVO();
        carpoolResult.setShowCarpool(false);

        // 创建UserPermissionRpcVO并设置必要字段
        UserPermissionRpcVO userPermissionRpcVO = new UserPermissionRpcVO();
        userPermissionRpcVO.setServicePermissionTypeId("1");
        userPermissionRpcVO.setTotalNum(10);
        userPermissionRpcVO.setUsedNum(5);

        // 创建DispatchCargoOwnerDO并设置必要字段
        DispatchCargoOwnerDO dispatchCargoOwnerDO = new DispatchCargoOwnerDO();
        dispatchCargoOwnerDO.setCooperativeId(1L);

        // 创建DispatchCooperativeDO并设置必要字段
        DispatchCooperativeDO dispatchCooperativeDO = new DispatchCooperativeDO();
        dispatchCooperativeDO.setId(1L);

        // 创建DepositBlockCheckResultRpcVO并设置必要字段
        DepositBlockCheckResultRpcVO depositBlockCheckResultRpcVO = new DepositBlockCheckResultRpcVO();
        depositBlockCheckResultRpcVO.setBlock(false);

        // 设置Mock行为
        when(calcSpecialGoodsPriceService.calculatePriceV2(any(CalculatePriceDTO.class))).thenReturn(calcResult);
        when(transportMainRpcService.queryById(anyLong())).thenReturn(transportMainVO);
        when(transportMainRpcService.getExtendBySrcMsgId(anyLong())).thenReturn(transportMainExtendVO);
        when(transportMainRpcService.processCarpool(any(PublishGoodsTypeDTO.class))).thenReturn(carpoolResult);
        when(userPermissionRemoteService.getUserPermissionByUserId(any(UserPermissionRpcDTO.class))).thenReturn(List.of(userPermissionRpcVO));
        when(dispatchCargoOwnerService.selectSignedByUserId(anyLong())).thenReturn(dispatchCargoOwnerDO);
        when(dispatchCooperativeService.selectByName(anyString())).thenReturn(dispatchCooperativeDO);
        when(thPriceRpcService.getThPrice(any(TransportCarryReq.class))).thenReturn(carryPriceVO);
        when(userLimitRemoteService.getDepositBlock(anyLong(), anyBoolean())).thenReturn(depositBlockCheckResultRpcVO);

        // 手动创建PublishGoodsTypeDTO并设置必要字段，避免空指针异常
        PublishGoodsTypeDTO publishGoodsTypeDTO = new PublishGoodsTypeDTO();
        publishGoodsTypeDTO.setUserId(1L);
        publishGoodsTypeDTO.setDistance(new BigDecimal("100"));
        publishGoodsTypeDTO.setGoodsWeight(new BigDecimal("10"));
        publishGoodsTypeDTO.setGoodsLength(new BigDecimal("5"));
        publishGoodsTypeDTO.setGoodsWide(new BigDecimal("3"));
        publishGoodsTypeDTO.setGoodsHigh(new BigDecimal("2"));

        // 设置地址信息，确保TytBeanUtil.convertBean能够正常工作
        publishGoodsTypeDTO.setStartProvince("北京市");
        publishGoodsTypeDTO.setStartCity("北京市");
        publishGoodsTypeDTO.setStartArea("朝阳区");
        publishGoodsTypeDTO.setDestProvince("上海市");
        publishGoodsTypeDTO.setDestCity("上海市");
        publishGoodsTypeDTO.setDestArea("浦东新区");
        publishGoodsTypeDTO.setGoodTypeName("普通货物");

        PublishGoodsTypeResultVO result = transportPublishTypeRpcServiceImpl.getPublishGoodsType(publishGoodsTypeDTO);

        Assertions.assertNotNull(result);
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme