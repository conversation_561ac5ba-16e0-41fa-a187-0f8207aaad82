package com.teyuntong.goods.service.service.biz.invoice.service.impl;

import com.teyuntong.goods.service.client.invoice.vo.PublishTransportInvoiceDominantVo;
import com.teyuntong.goods.service.client.invoice.vo.PublishTransportInvoiceVo;

import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportEnterpriseLogDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportEnterpriseLogMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainMapper;
import com.teyuntong.goods.service.service.remote.user.InvoiceEnterpriseRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.enterprise.vo.InvoiceDominantVo;
import com.teyuntong.user.service.client.enterprise.vo.InvoiceEnterpriseInfoVO;
import org.junit.jupiter.api.AfterEach;
import org.mockito.MockedStatic;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.mock.web.MockHttpServletRequest;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Base64;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InvoiceEnterpriseServiceImpl 单元测试类
 *
 * 注意：由于项目使用标准Mockito，不支持静态方法模拟，
 * 因此这些测试主要验证远程调用和数据库查询的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@ExtendWith(MockitoExtension.class)
class InvoiceEnterpriseServiceImplTest {

    @Mock
    private ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;

    @Mock
    private InvoiceEnterpriseRemoteService invoiceEnterpriseRemoteService;

    @Mock
    private TransportMainMapper transportMainMapper;

    @Mock
    private TransportEnterpriseLogMapper transportEnterpriseLogMapper;

    @InjectMocks
    private InvoiceEnterpriseServiceImpl invoiceEnterpriseService;


    private MockedStatic<LoginHelper> loginHelperMock;

    @BeforeEach
    void setUp() {
        // 设置测试环境
        loginHelperMock = mockStatic(LoginHelper.class);
    }

    @AfterEach
    void tearDown() {
        // 关闭静态模拟
        if (loginHelperMock != null) {
            loginHelperMock.close();
        }
    }

    /**
     * 测试成功获取发布运输发票信息
     *
     * 通过模拟 RequestContextHolder 来设置登录用户信息，解决 LoginHelper 静态方法调用问题
     */
    @Test
    void testSuccess() throws Exception {
        // 准备测试数据
        Long testUserId = 123L;

        // 创建模拟的登录用户
        LoginUserDTO mockLoginUser = new LoginUserDTO();
        mockLoginUser.setUserId(testUserId);

        // 模拟 LoginHelper.getRequiredLoginUser() 静态方法
        loginHelperMock.when(LoginHelper::getRequiredLoginUser).thenReturn(mockLoginUser);

        // 准备测试数据 - 模拟远程服务返回空列表
        List<InvoiceDominantVo> invoiceDominantList = new ArrayList<>();
        InvoiceDominantVo invoiceDominantVo = new InvoiceDominantVo();
        invoiceDominantVo.setDominantId(1001L);
        invoiceDominantVo.setServiceProviderCode("PROVIDER_A");
        invoiceDominantVo.setTaxRate(new BigDecimal("0.10"));
        invoiceDominantList.add(invoiceDominantVo);
        invoiceDominantVo = new InvoiceDominantVo();
        invoiceDominantVo.setDominantId(1002L);
        invoiceDominantVo.setServiceProviderCode("PROVIDER_B");
        invoiceDominantVo.setTaxRate(new BigDecimal("0.05"));
        invoiceDominantList.add(invoiceDominantVo);
//            invoiceDominantVo = new InvoiceDominantVo();
//            invoiceDominantVo.setDominantId(1003L);
//            invoiceDominantVo.setServiceProviderCode("PROVIDER_C");
//            invoiceDominantVo.setTaxRate(new BigDecimal("0.07"));
//            invoiceDominantList.add(invoiceDominantVo);
        invoiceDominantVo = new InvoiceDominantVo();
        invoiceDominantVo.setDominantId(1004L);
        invoiceDominantVo.setServiceProviderCode("PROVIDER_C");
        invoiceDominantVo.setTaxRate(new BigDecimal("0.08"));
        invoiceDominantList.add(invoiceDominantVo);

        TransportMainDO transportMainDO = new TransportMainDO();
        transportMainDO.setSrcMsgId(12345L);
        transportMainDO.setInvoiceTransport(1);

        TransportEnterpriseLogDO transportEnterpriseLogDO = new TransportEnterpriseLogDO();
        transportEnterpriseLogDO.setInvoiceSubjectId(1003L);
        transportEnterpriseLogDO.setServiceProviderCode("PROVIDER_C");

        when(thirdEnterpriseRemoteService.getInvoiceDominantList(anyLong())).thenReturn(WebResult.success(invoiceDominantList));

//            when(invoiceEnterpriseRemoteService.getInfoByUserId(anyLong())).thenReturn(new InvoiceEnterpriseInfoVO());

        when(transportMainMapper.getLastTransportByTransportUserId(anyLong())).thenReturn(transportMainDO);

        when(transportEnterpriseLogMapper.getBySrcMsgId(anyLong())).thenReturn(transportEnterpriseLogDO);

        PublishTransportInvoiceVo publishTransportInvoice = invoiceEnterpriseService.getPublishTransportInvoice();
        System.out.println(1);


    }


    @Test
    void testRemoteServiceCall_WithEmptyResult() {
        // 准备测试数据 - 模拟远程服务返回空列表
        when(thirdEnterpriseRemoteService.getInvoiceDominantList(anyLong()))
                .thenReturn(WebResult.success(new ArrayList<>()));

        // 由于无法模拟静态方法，这个测试主要验证远程服务调用的模拟
        // 验证远程服务被正确模拟
        WebResult<List<InvoiceDominantVo>> result = thirdEnterpriseRemoteService.getInvoiceDominantList(123L);
        assertNotNull(result);
        assertTrue(result.ok());
        assertTrue(result.getData().isEmpty());

        // 验证方法调用
        verify(thirdEnterpriseRemoteService).getInvoiceDominantList(123L);
    }

    @Test
    void testRemoteServiceCall_WithFailedResult() {
        // 准备测试数据 - 模拟远程服务返回失败结果
        when(thirdEnterpriseRemoteService.getInvoiceDominantList(anyLong()))
                .thenReturn(WebResult.error(CommonErrorCode.INTERNAL_ERROR));

        // 验证远程服务被正确模拟
        WebResult<List<InvoiceDominantVo>> result = thirdEnterpriseRemoteService.getInvoiceDominantList(123L);
        assertNotNull(result);
        assertFalse(result.ok());

        // 验证方法调用
        verify(thirdEnterpriseRemoteService).getInvoiceDominantList(123L);
    }

    @Test
    void testTransportMainMapper_GetLastTransport() {
        // 准备测试数据
        Long userId = 123L;
        TransportMainDO mockTransport = new TransportMainDO();
        mockTransport.setSrcMsgId(67890L);
        mockTransport.setInvoiceTransport(1);

        // 模拟数据库查询
        when(transportMainMapper.getLastTransportByTransportUserId(userId)).thenReturn(mockTransport);

        // 执行测试
        TransportMainDO result = transportMainMapper.getLastTransportByTransportUserId(userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(67890L, result.getSrcMsgId());
        assertEquals(1, result.getInvoiceTransport());

        // 验证方法调用
        verify(transportMainMapper).getLastTransportByTransportUserId(userId);
    }

    @Test
    void testTransportEnterpriseLogMapper_GetBySrcMsgId() {
        // 准备测试数据
        Long srcMsgId = 67890L;
        TransportEnterpriseLogDO mockLog = new TransportEnterpriseLogDO();
        mockLog.setInvoiceSubjectId(1001L);
        mockLog.setServiceProviderCode("PROVIDER_A");

        // 模拟数据库查询
        when(transportEnterpriseLogMapper.getBySrcMsgId(srcMsgId)).thenReturn(mockLog);

        // 执行测试
        TransportEnterpriseLogDO result = transportEnterpriseLogMapper.getBySrcMsgId(srcMsgId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1001L, result.getInvoiceSubjectId());
        assertEquals("PROVIDER_A", result.getServiceProviderCode());

        // 验证方法调用
        verify(transportEnterpriseLogMapper).getBySrcMsgId(srcMsgId);
    }

    @Test
    void testInvoiceDominantVo_MockCreation() {
        // 测试模拟InvoiceDominantVo对象的创建
        InvoiceDominantVo mockVo = mock(InvoiceDominantVo.class);
        when(mockVo.getDominantId()).thenReturn(1001L);
        when(mockVo.getServiceProviderCode()).thenReturn("PROVIDER_A");
        when(mockVo.getTaxRate()).thenReturn(new BigDecimal("0.06"));

        // 验证模拟对象
        assertEquals(1001L, mockVo.getDominantId());
        assertEquals("PROVIDER_A", mockVo.getServiceProviderCode());
        assertEquals(new BigDecimal("0.06"), mockVo.getTaxRate());

        // 验证方法调用
        verify(mockVo).getDominantId();
        verify(mockVo).getServiceProviderCode();
        verify(mockVo).getTaxRate();
    }

}
