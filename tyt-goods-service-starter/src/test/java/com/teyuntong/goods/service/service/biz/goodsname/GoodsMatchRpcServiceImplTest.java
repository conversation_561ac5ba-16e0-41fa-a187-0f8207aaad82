package com.teyuntong.goods.service.service.biz.goodsname;

import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.goodsname.dto.CheckScreeningWordDto;
import com.teyuntong.goods.service.client.goodsname.dto.GoodsMatchRpcDto;
import com.teyuntong.goods.service.client.goodsname.service.GoodsMatchRpcService;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchVo;
import com.teyuntong.goods.service.client.quotedprice.service.TransportQuotedPriceRpcService;
import com.teyuntong.goods.service.client.quotedprice.vo.CarShowQuotedPriceBoxVO;
import com.teyuntong.goods.service.service.biz.goodsname.service.GoodsMatchService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.utils.IKAnalyzerUtils;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.checker.ExposureCardUseChecker;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * 客户资料服务测试类
 *
 * <AUTHOR>
 * @since 2024-01-18 16:07
 */
@Slf4j
@Disabled("老旧单元测试")
class GoodsMatchRpcServiceImplTest extends TestBase {

    @Autowired
    private GoodsMatchRpcService goodsMatchRpcService;
    @Autowired
    private GoodsMatchService goodsMatchService;
    @Autowired
    private IKAnalyzerUtils ikAnalyzerUtils;
    @Autowired
    private TransportQuotedPriceRpcService transportQuotedPriceRpcService;

    @Autowired
    private ExposureCardUseChecker exposureCardUseChecker;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private TransportMainExtendService transportMainExtendService;


    @Test
    void searchGoodsMatch() {
        GoodsMatchRpcDto goodsMatchRpcDto = new GoodsMatchRpcDto();
        goodsMatchRpcDto.setKeywords("挖装机");
        goodsMatchRpcDto.setStandardName("挖掘机,打桩机");
        goodsMatchRpcDto.setPageNum(3);
        GoodsMatchVo goodsMatchVo = goodsMatchRpcService.searchGoodsMatch(goodsMatchRpcDto);
        System.out.println(JSONUtil.toJsonStr(goodsMatchVo));
    }

    @Test
    void analysisKeywordsTest() {
        String text = "小麦5000收割机";
        List<String> analyze = ikAnalyzerUtils.analyze(text);
        System.out.println(JSONUtil.toJsonStr(analyze));
    }

    @Test
    void test432432() {
        CarShowQuotedPriceBoxVO carShowQuotedPriceBoxVO = transportQuotedPriceRpcService.carShowQuotedPriceBox(88824435L);
        System.out.println(1);
    }


    @Test
    void checkScreeningWordTest() {
        String text = "小麦收割机";
        CheckScreeningWordDto checkScreeningWordDto = new CheckScreeningWordDto();
        checkScreeningWordDto.setKeywords(text);


        goodsMatchRpcService.screeningWordCheck(checkScreeningWordDto);
    }

    @Test
    void test4234324() {
        long srcMsgId = 106578399L;
        long userId = 1002000800L;

        DirectPublishProcessBO processBO = new DirectPublishProcessBO();
        TransportMainDO transportMainDO = transportMainService.getTransportMainForId(srcMsgId);
        TransportMainExtendDO transportMainExtendDO = transportMainExtendService.getBySrcMsgId(srcMsgId);

        UserRpcVO userRpcVO = new UserRpcVO();
        userRpcVO.setId(userId);

        DirectPublishBO directPublishBO = new DirectPublishBO();
        directPublishBO.setSrcMsgId(srcMsgId);

        processBO.setUser(userRpcVO);
        processBO.setOldMain(transportMainDO);
        processBO.setOldMainExtend(transportMainExtendDO);
        processBO.setDirectPublishBO(directPublishBO);

        exposureCardUseChecker.checkExposureCardUseLimit(processBO);
    }
}
