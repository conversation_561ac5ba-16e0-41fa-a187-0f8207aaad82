package com.teyuntong.goods.service.service.biz.transport;

import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.quotedprice.vo.TransportQuotedPriceTransportVO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportQuotedPriceService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025-05-21 11:34
 */
@Disabled("老旧单元测试")
public class TransportQuotedPriceServiceTest extends TestBase {
    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;

    @Test
    public void getQuotedPriceListInSingleDetailPage() {
        List<TransportQuotedPriceTransportVO> quotedPriceListInSingleDetailPage = transportQuotedPriceService.getQuotedPriceListInSingleDetailPage(88824092L, 1002000056L);

    }
}
