package com.teyuntong.goods.service.service.rpc.invoice;

import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.invoice.service.InvoiceConfigRpcService;
import com.teyuntong.goods.service.client.invoice.vo.InvoiceTransportConfigLogVO;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportConfigLogDTO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceTransportPriceEnterpriseConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 开票配置RPC服务测试类
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Disabled("老旧单元测试")
class InvoiceConfigRpcServiceImplTest extends TestBase {

    @Autowired
    private InvoiceConfigRpcServiceImpl transportPriceEnterpriseConfigService;

    @Test
    void testGetLastInvoiceTransportEnterpriseConfig() {
        // 测试参数
        Long enterpriseId = 889999999L;
        String serviceProviderCode = "JCZY";

        try {
            // 调用RPC接口
            InvoiceTransportConfigLogVO result = transportPriceEnterpriseConfigService
                    .getLastInvoiceTransportEnterpriseConfig(enterpriseId, serviceProviderCode);

            // 输出结果
            log.info("测试结果: {}", result != null ? "有配置数据" : "无配置数据");
            if (result != null) {
                log.info("配置ID: {}", result.getId());
                log.info("最大吨位: {}", result.getMaxTonnage());
                log.info("最大长度: {}", result.getMaxLength());
                log.info("最大宽度: {}", result.getMaxWidth());
                log.info("最大高度: {}", result.getMaxHeight());
                log.info("价格配置数量: {}",
                        result.getTytInvoiceTransportPriceConfigList() != null ?
                        result.getTytInvoiceTransportPriceConfigList().size() : 0);

                // 验证价格配置列表的转换
                if (result.getTytInvoiceTransportPriceConfigList() != null &&
                    !result.getTytInvoiceTransportPriceConfigList().isEmpty()) {
                    log.info("第一个价格配置 - 吨位范围: {}-{}, 距离范围: {}-{}, 最大价格: {}",
                            result.getTytInvoiceTransportPriceConfigList().get(0).getLowTonnageValue(),
                            result.getTytInvoiceTransportPriceConfigList().get(0).getHighTonnageValue(),
                            result.getTytInvoiceTransportPriceConfigList().get(0).getLowDistanceValue(),
                            result.getTytInvoiceTransportPriceConfigList().get(0).getHighDistanceValue(),
                            result.getTytInvoiceTransportPriceConfigList().get(0).getMaxPrice());
                }
            }

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    void testGetLastInvoiceTransportEnterpriseConfigWithNullEnterprise() {
        // 测试空企业ID的情况
        Long enterpriseId = null;
        String serviceProviderCode = "JCZY";

        try {
            InvoiceTransportConfigLogVO result = transportPriceEnterpriseConfigService
                    .getLastInvoiceTransportEnterpriseConfig(enterpriseId, serviceProviderCode);

            log.info("空企业ID测试结果: {}", result != null ? "有配置数据" : "无配置数据");

        } catch (Exception e) {
            log.error("空企业ID测试失败", e);
        }
    }

    @Test
    void testGetLastInvoiceTransportEnterpriseConfigWithDifferentProviders() {
        // 测试不同服务提供商
        Long enterpriseId = 1L;
        String[] providers = {"JCZY", "HBWJ", "AHST", "XHL"};

        for (String provider : providers) {
            try {
                InvoiceTransportConfigLogVO result = transportPriceEnterpriseConfigService
                        .getLastInvoiceTransportEnterpriseConfig(enterpriseId, provider);

                log.info("服务提供商 {} 测试结果: {}", provider, 
                        result != null ? "有配置数据" : "无配置数据");

            } catch (Exception e) {
                log.error("服务提供商 {} 测试失败", provider, e);
            }
        }
    }
}
