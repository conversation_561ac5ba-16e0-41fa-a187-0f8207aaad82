package com.teyuntong.goods.service.service.biz.order;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.transport.service.PublishHistoryRpcService;
import com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO;
import com.teyuntong.goods.service.client.transport.vo.PublishTaskContentHistoryVO;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceResultDTO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运输管理后台测试类
 *
 * <AUTHOR>
 * @since 2024-01-18 16:08
 */
@Slf4j
@Disabled("老旧单元测试")
class TransportAfterOrderDataServiceTest extends TestBase {

    @Autowired
    private TransportAfterOrderDataService transportAfterOrderDataService;

    @Autowired
    private PublishHistoryRpcService publishHistoryRpcService;

    @Autowired
    private TransportMainService transportMainService;

    @Test
    void getSameTransportAvgPrice() {
        SameTransportAvgPriceResultDTO sameTransportAvgPrice =
                transportAfterOrderDataService.getSameTransportAvgPrice(88823594L, null);
        System.out.println(JSON.toJSONString(sameTransportAvgPrice));
    }

    @Test
    void getSameTransportAvgPrice2() {
        BigDecimal sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPriceByDays(88823594L, 30);
        log.info("{}", sameTransportAvgPrice);
    }

    @Test
    void test4324() {
        List<PublishLocationHistoryVO> startLocationHistory = publishHistoryRpcService.getStartLocationHistory(1002001075L);
        List<PublishLocationHistoryVO> destLocationHistory = publishHistoryRpcService.getDestLocationHistory(1002001075L);
        List<PublishTaskContentHistoryVO> taskContentHistory = publishHistoryRpcService.getTaskContentHistory(1002001075L);
        System.out.println(1);
    }

}



