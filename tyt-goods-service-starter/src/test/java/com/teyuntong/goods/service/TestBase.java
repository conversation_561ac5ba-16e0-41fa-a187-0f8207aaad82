package com.teyuntong.goods.service;

import com.teyuntong.goods.service.starter.TytGoodsServiceApplication;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;

@AutoConfigureMockMvc
@SpringBootTest(classes = TytGoodsServiceApplication.class)
@ContextConfiguration
@Disabled("老旧单元测试")
public class TestBase {

    @Autowired
    protected MockMvc mockMvc;
}
