package com.teyuntong.goods.service.starter;

import com.teyuntong.infra.common.retrofit.core.EnableRetrofitClient;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.easyes.starter.register.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;


@EnableFeignClients(basePackages = "com.teyuntong")
@MapperScan(basePackages = "com.teyuntong", annotationClass = Mapper.class)
@SpringBootApplication(scanBasePackages = "com.teyuntong")
@EnableRetrofitClient(basePackages = "com.teyuntong")
@EsMapperScan(value = "com.teyuntong.goods.service.service.biz.goodsname.mybatis.es.mapper")
@EnableAsync
public class TytGoodsServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(TytGoodsServiceApplication.class, args);
    }

}