PUT /machine_type
{
  "settings" : {
    "index" : {
      "number_of_shards" : "1",
      "number_of_replicas" : "1",
      "analysis" : {
        "analyzer" : {
          "tyt_analyzer" : {
            "tokenizer" : "ik_smart",
            "filter" : [
              "tyt_synonym_filter"
            ]
          }
        },
        "filter":{
          "tyt_synonym_filter" : {
            "type" : "synonym_graph",
            "synonyms_path" : "analysis/tyt_synonyms.txt"
          }
        }
      },
      "similarity": {
        "tyt_similarity": {
          "type": "BM25",
          "discount_overlaps": false
        }
      }
    }
  },
  "mappings" : {
    "properties" : {
      "id" : {
        "type" : "long"
      },
      "show_name" : {
        "type" : "text",
        "fields" : {
          "keyword" : {
            "type" : "keyword",
            "ignore_above" : 256
          }
        },
        "index_options" : "docs",
        "analyzer" : "tyt_analyzer",
        "similarity": "tyt_similarity"
      },
      "second_class" : {
        "type" : "keyword"
      },
      "brand" : {
        "type" : "keyword"
      },
      "top_type" : {
        "type" : "keyword"
      },
      "display" : {
        "type" : "integer"
      },
      "ctime" : {
        "type" : "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "mtime" : {
        "type" : "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "brand_type" : {
        "type" : "keyword",
        "index" : false
      },
      "height" : {
        "type" : "double",
        "index":false
      },
      "length" : {
        "type" : "double",
        "index":false
      },
      "remarks" : {
        "type" : "text",
        "index":false
      },
      "score" : {
        "type" : "double",
        "index":false
      },
      "second_type" : {
        "type" : "keyword",
        "index" : false
      },
      "top_class" : {
        "type" : "keyword",
        "index" : false
      },
      "weight" : {
        "type" : "double",
        "index":false
      },
      "width" : {
        "type" : "double",
        "index":false
      },
      "general_matches_item" : {
        "type" : "keyword",
        "index":false
      }
    }
  }
}
