tyt:
  # 提示文案
  prompt:
    # 货源诊断
    goods-diagnosis:
      # 类型：0模块标题，1去填价，2加价，3转一口价，4补全货物信息，5调整装货时间
      # title标题，prompt文案，x为需要替换值的文案，p优先级更高，since-version最低版本，小于这个版本的不显示
      0:
        title: <span style="font-size:14px;font-weight:700;">正在通知附近司机</span>
        title-x: <span style="font-size:14px;font-weight:700;">正在通知附近司机，已有<font color="#FF5B00">X</font>位查看</span>
      1:
        title: <span style="font-size:14px;font-weight:700;">填写运费</span>
        prompt: 未填写运费，查看司机较少
        title-x: <span style="font-size:14px;font-weight:700;">填写运费</span>
        prompt-x: 未填写运费，查看司机较少
        title-p: <span style="font-size:14px;font-weight:700;">填写运费</span>
        prompt-p: 未填写运费，查看司机较少
      2:
        title: <span style="font-size:14px;font-weight:700;">运费加价</span>
        prompt: 运力紧张,加价找车更快
        title-x: <span style="font-size:14px;font-weight:700;">运费加价</span>
        prompt-x: 运力紧张，加价找车更快
        title-s-1: <span style="font-size:14px;font-weight:700;">运费加价</span>
        prompt-s-1: 运力紧张，加价找车更快
        title-s-2: <span style="font-size:14px;font-weight:700;">运费加价</span>
        prompt-s-2: 运力紧张，加价找车更快
      3:
        title: <span style="font-size:14px;font-weight:700;">抢货源置顶位</span>
        prompt: 扩大推流范围
        title-x: <span style="font-size:14px;font-weight:700;">线上已有X票相似货源</span>
        prompt-x: 扩大推流范围
      4:
        title: <span style="font-size:14px;font-weight:700;">补全货源信息</span>
        prompt: 信息不完整，查看司机减半
        prompt-x: 信息不完整，查看司机减半
        since-version: 6630
      6:
        title: <span style="font-size:14px;font-weight:700;">货源同步至多平台找车</span>
        prompt: 同步后，找车更快
        since-version: 6690
      5:
        title: <span style="font-size:14px;font-weight:700;">用曝光卡，获得更多流量</span>
        prompt: 曝光后，为您的货推送更多司机
        title-x: <span style="font-size:14px;font-weight:700;">用曝光卡，抢占置顶位置</span>
        prompt-x: 相似货源较多，曝光得更多流量
        since-version: 6640
      7:
        title: <span style="font-size:14px;font-weight:700;">开启回价助手，加速成交</span>
        prompt: 留住司机，避免错失走货机会
        since-version: 6690
    # 我的货源页点击直接发布弹出出价弹窗文案
    popup-price-box:
      prompt-1: <span style="font-size:14px;">您上一票该货源以<span style="font-weight:700;font-size:16px;">X</span>元成交</span>
      prompt-2: <span style="font-size:14px;">您上一票相似货源以<span style="font-weight:700;font-size:16px;">X</span>元成交</span>
      prompt-3: <span style="font-size:14px;">刚刚有<span style="font-weight:700;font-size:16px;">Y</span>票相似货源以<span style="font-weight:700;font-size:16px;">X</span>元成交</span>
    # 我的货源页点击撤销弹出挽留弹窗文案
    show-retention-box:
      title-price-x: <span style="font-size:20px;">近7天相似货源成交价<span style="color:#FF5B00;">X元</span></span>
      title-priceless-x: <span style="font-size:20px;">司机预计还有<span style="color:#FF5B00;">X分钟</span>接单了，再等等~</span>
      title-price: <span style="font-size:20px;">可尝试加价获<span style="color:#FF5B00;">曝光</span>，提高找车速度</span>
      title-priceless: <span style="font-size:20px;">可尝试填价获<span style="color:#FF5B00;">曝光</span>，提高找车速度</span>
      task-price: <span style="font-size:14px;font-weight:700;">加点小钱，司机更愿意接单</span>
      task-priceless: <span style="font-size:14px;font-weight:700;">电议有价 成交更快</span>
      task-republish: <span style="font-size:14px;font-weight:700;">修改信息，重新发布</span>
    # 点击货源发布弹出出价弹窗文案
    publish-popup-price-box:
      title-has-similar: 您的货与<span style="color:#FF5B00;">X票</span>货高度相似
      title-no-similar: 未填写运费，找车较慢
      content: 填价后货源<span style="color:#FF5B00;">优先展示</span>，找车更快
      prompt: 近X天相似货源平均成交价<span style="color:#FF5B00;">Y元</span>
