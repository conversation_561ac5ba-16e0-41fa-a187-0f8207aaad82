package com.teyuntong.goods.service.mybatis.generator.mbp;

import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/11/29 15:56
 */
@Data
@ConfigurationProperties(prefix = "mbp.generator")
public class MbpGeneratorProperties {

    /**
     * 数据库配置, 可配置多个, map类型
     */
    private Map<String, DataSourceProperties> dataSources = Maps.newHashMap();
    /**
     * 全局配置
     */
    private GlobalConfig globalConfig = new GlobalConfig();
    /**
     * 包配置
     */
    private PackageConfig packageConfig = new PackageConfig();
    /**
     * 模板配置
     */
    private TemplateConfig templateConfig = new TemplateConfig();
    /**
     * 原配置太多了, 只拿了几个出来, 有需要的自行修改代码
     */
    private StrategyConfig strategyConfig = new StrategyConfig();

    @Data
    public static class DataSourceProperties {
        /**
         * url
         */
        private String url;
        /**
         * 用户名
         */
        private String username;
        /**
         * 密码
         */
        private String password;
    }

    @Data
    public static class GlobalConfig {
        /**
         * 生成文件的输出目录, 默认 .generate
         */
        private String outputDir = ".generate";

        /**
         * 生成文件的路径模式, relative 相对路径, 其他绝对路径
         */
        private String outputDirType = "relative";

        /**
         * 是否打开输出目录
         */
        private boolean open = true;

        /**
         * 作者
         */
        private String author = "mybatis-plus 自动生成";

        /**
         * 开启 Kotlin 模式（默认 false）
         */
        private boolean kotlin = false;

        /**
         * 开启 swagger 模式（默认 false 与 springdoc 不可同时使用）
         */
        private boolean swagger = false;
        /**
         * 开启 springdoc 模式（默认 false 与 swagger 不可同时使用）
         */
        private boolean springdoc = false;

        /**
         * 时间类型对应策略
         */
        private DateType dateType = DateType.ONLY_DATE;

        /**
         * 是否生成service 接口（默认 true）
         * 增加此开关的原因：在某些项目实践中，只需要生成service实现类，不需要抽象sevice接口
         * 针对某些项目，生成service接口，开发时反而麻烦，这种情况，可以将该属性设置为false
         */
        private boolean serviceInterface = true;
    }

    @Data
    public static class PackageConfig {
        /**
         * 父包名。如果为空，将下面子包名必须写全部， 否则就只需写子包名
         */
        private String parent = "com.teyuntong";

        /**
         * 父包模块名
         */
        private String moduleName = "";

        /**
         * Entity包名
         */
        private String entity = "entity";

        /**
         * Service包名
         */
        private String service = "service";

        /**
         * Service Impl包名
         */
        private String serviceImpl = "service.impl";

        /**
         * Mapper包名
         */
        private String mapper = "mapper";

        /**
         * Mapper XML包名
         */
        private String xml = "mapper.xml";

        /**
         * Controller包名
         */
        private String controller = "controller";
    }

    @Data
    public static class TemplateConfig {
        /**
         * 要禁用的模板类型
         */
        private List<TemplateType> disableTypes = Lists.newArrayList();
        /**
         * 设置实体模板路径
         */
        private String entity;

        /**
         * 设置实体模板路径(kotlin模板)
         */
        private String entityKt;

        /**
         * 设置控制器模板路径
         */
        private String controller;

        /**
         * 设置Mapper模板路径
         */
        private String mapper;

        /**
         * 设置MapperXml模板路径
         */
        private String xml;

        /**
         * 设置Service模板路径
         */
        private String service;

        /**
         * 设置ServiceImpl模板路径
         */
        private String serviceImpl;
    }

    @Data
    public static class StrategyConfig {
        /**
         * 需要包含的表名，允许正则表达式（与exclude二选一配置）<br/>
         * 当{@link #enableSqlFilter}为true时，正则表达式无效.
         */
        private final Set<String> include = new HashSet<>();

        /**
         * 启用sql过滤，语法不能支持使用sql过滤表的话，可以考虑关闭此开关.
         *
         * @since 3.3.1
         */
        private boolean enableSqlFilter = true;

        /**
         * 是否覆盖已有 service 文件（默认 false）
         */
        private boolean serviceOverride = false;
        /**
         * 是否覆盖已有 mapper 文件（默认 false）
         */
        private boolean mapperOverride = false;
        /**
         * 是否覆盖已有 entity 文件（默认 true）
         */
        private boolean entityOverride = true;

    }
}
